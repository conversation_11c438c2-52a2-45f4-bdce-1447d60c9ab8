package com.yingfei.common.redis.service;

import com.yingfei.common.redis.util.RedisRetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 增强版 Redis 服务类
 * 集成重试机制，提供更可靠的 Redis 操作
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class EnhancedRedisService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // ========== 基础操作 ==========

    /**
     * 设置缓存对象（带重试机制）
     */
    public <T> void setCacheObject(final String key, final T value) {
        RedisRetryUtil.executeWithRetry(() -> {
            redisTemplate.opsForValue().set(key, value);
        }, "设置缓存对象: " + key);
    }

    /**
     * 设置缓存对象（带超时和重试机制）
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        RedisRetryUtil.executeWithRetry(() -> {
            redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
        }, "设置缓存对象(带超时): " + key);
    }

    /**
     * 获取缓存对象（带重试机制）
     */
    public <T> T getCacheObject(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return (T) redisTemplate.opsForValue().get(key);
        }, "获取缓存对象: " + key);
    }

    /**
     * 删除缓存对象（带重试机制）
     */
    public boolean deleteObject(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.delete(key);
        }, "删除缓存对象: " + key);
    }

    /**
     * 批量删除缓存对象（带重试机制）
     */
    public boolean deleteObject(final Collection<String> keys) {
        return RedisRetryUtil.executeWithRetry(() -> {
            Long deletedCount = redisTemplate.delete(keys);
            return deletedCount != null && deletedCount > 0;
        }, "批量删除缓存对象: " + keys.size() + " 个");
    }

    /**
     * 设置过期时间（带重试机制）
     */
    public boolean expire(final String key, final long timeout, final TimeUnit timeUnit) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.expire(key, timeout, timeUnit);
        }, "设置过期时间: " + key);
    }

    /**
     * 获取过期时间（带重试机制）
     */
    public long getExpire(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.getExpire(key);
        }, "获取过期时间: " + key);
    }

    /**
     * 判断键是否存在（带重试机制）
     */
    public boolean hasKey(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.hasKey(key);
        }, "判断键是否存在: " + key);
    }

    // ========== List 操作 ==========

    /**
     * 缓存 List 数据（带重试机制）
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        return RedisRetryUtil.executeWithRetry(() -> {
            Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
            return count != null ? count : 0;
        }, "缓存List数据: " + key);
    }

    /**
     * 缓存 List 数据（带超时和重试机制）
     */
    public <T> long setCacheList(final String key, final List<T> dataList, final long timeout, final TimeUnit timeUnit) {
        return RedisRetryUtil.executeWithRetry(() -> {
            Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
            if (timeout > 0) {
                redisTemplate.expire(key, timeout, timeUnit);
            }
            return count != null ? count : 0;
        }, "缓存List数据(带超时): " + key);
    }

    /**
     * 获取缓存的 List 对象（带重试机制）
     */
    public <T> List<T> getCacheList(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.opsForList().range(key, 0, -1);
        }, "获取缓存List: " + key);
    }

    // ========== Set 操作 ==========

    /**
     * 缓存 Set 数据（带重试机制）
     */
    public <T> long setCacheSet(final String key, final Set<T> dataSet) {
        return RedisRetryUtil.executeWithRetry(() -> {
            Long count = redisTemplate.opsForSet().add(key, dataSet.toArray());
            return count != null ? count : 0;
        }, "缓存Set数据: " + key);
    }

    /**
     * 获取缓存的 Set（带重试机制）
     */
    public <T> Set<T> getCacheSet(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.opsForSet().members(key);
        }, "获取缓存Set: " + key);
    }

    // ========== Hash 操作 ==========

    /**
     * 缓存 Map（带重试机制）
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        RedisRetryUtil.executeWithRetry(() -> {
            if (dataMap != null && !dataMap.isEmpty()) {
                redisTemplate.opsForHash().putAll(key, dataMap);
            }
        }, "缓存Map数据: " + key);
    }

    /**
     * 缓存 Map（带超时和重试机制）
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap, final long timeout, final TimeUnit timeUnit) {
        RedisRetryUtil.executeWithRetry(() -> {
            if (dataMap != null && !dataMap.isEmpty()) {
                redisTemplate.opsForHash().putAll(key, dataMap);
                if (timeout > 0) {
                    redisTemplate.expire(key, timeout, timeUnit);
                }
            }
        }, "缓存Map数据(带超时): " + key);
    }

    /**
     * 获得缓存的 Map（带重试机制）
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.opsForHash().entries(key);
        }, "获取缓存Map: " + key);
    }

    /**
     * 往 Hash 中存入数据（带重试机制）
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        RedisRetryUtil.executeWithRetry(() -> {
            redisTemplate.opsForHash().put(key, hKey, value);
        }, "设置Hash值: " + key + "." + hKey);
    }

    /**
     * 获取 Hash 中的数据（带重试机制）
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return (T) redisTemplate.opsForHash().get(key, hKey);
        }, "获取Hash值: " + key + "." + hKey);
    }

    /**
     * 删除 Hash 中的数据（带重试机制）
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.opsForHash().delete(key, hKey) > 0;
        }, "删除Hash值: " + key + "." + hKey);
    }

    // ========== 分布式锁操作 ==========

    /**
     * 尝试获取分布式锁（带重试机制）
     */
    public boolean tryLock(final String key, final String value, final long timeout, final TimeUnit timeUnit) {
        return RedisRetryUtil.executeWithRetry(() -> {
            return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, timeUnit);
        }, "尝试获取分布式锁: " + key);
    }

    /**
     * 释放分布式锁（带重试机制）
     */
    public boolean releaseLock(final String key, final String value) {
        return RedisRetryUtil.executeWithRetry(() -> {
            // 使用 Lua 脚本确保原子性
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Long result = redisTemplate.execute(
                (org.springframework.data.redis.core.script.RedisScript<Long>) 
                org.springframework.data.redis.core.script.RedisScript.of(script, Long.class),
                java.util.Collections.singletonList(key), 
                value
            );
            return result != null && result > 0;
        }, "释放分布式锁: " + key);
    }

    // ========== 高级重试配置 ==========

    /**
     * 使用自定义重试配置执行操作
     */
    public <T> T executeWithCustomRetry(java.util.function.Supplier<T> operation, String operationName, 
                                      int maxRetries, long initialDelayMs) {
        return RedisRetryUtil.executeWithRetry(operation, operationName, maxRetries, initialDelayMs);
    }

    /**
     * 使用构建器模式执行操作
     */
    public RedisRetryUtil.RetryConfigBuilder retryBuilder() {
        return RedisRetryUtil.builder();
    }
}

package com.yingfei.common.redis.test;

import com.yingfei.common.redis.monitor.RedisHealthMonitor;
import com.yingfei.common.redis.service.EnhancedRedisService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * Redis 连接测试类
 * 用于验证 Redis 配置和连接是否正常
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class RedisConnectionTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private EnhancedRedisService enhancedRedisService;

    @Autowired
    private RedisHealthMonitor redisHealthMonitor;

    private static final String TEST_KEY_PREFIX = "redis:test:";

    @PostConstruct
    public void init() {
        log.info("🧪 Redis 连接测试组件已初始化");
    }

    /**
     * 执行完整的 Redis 连接测试
     */
    public void performFullTest() {
        log.info("🚀 开始执行 Redis 连接测试...");
        
        try {
            // 1. 基础连接测试
            testBasicConnection();
            
            // 2. RedisTemplate 测试
            testRedisTemplate();
            
            // 3. Redisson 测试
            testRedissonClient();
            
            // 4. 增强服务测试
            testEnhancedRedisService();
            
            // 5. 健康监控测试
            testHealthMonitor();
            
            // 6. 性能测试
            testPerformance();
            
            log.info("✅ Redis 连接测试全部通过！");
            
        } catch (Exception e) {
            log.error("❌ Redis 连接测试失败: {}", e.getMessage(), e);
            throw new RuntimeException("Redis 连接测试失败", e);
        }
    }

    /**
     * 基础连接测试
     */
    private void testBasicConnection() {
        log.info("📡 测试基础连接...");
        
        try {
            // 测试 ping
            String pong = redisTemplate.execute(connection -> {
                return connection.ping();
            });
            
            if (!"PONG".equals(pong)) {
                throw new RuntimeException("Ping 测试失败，返回: " + pong);
            }
            
            log.info("✅ 基础连接测试通过");
            
        } catch (Exception e) {
            log.error("❌ 基础连接测试失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * RedisTemplate 测试
     */
    private void testRedisTemplate() {
        log.info("🔧 测试 RedisTemplate...");
        
        String testKey = TEST_KEY_PREFIX + "template:" + System.currentTimeMillis();
        String testValue = "RedisTemplate测试值_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        
        try {
            // 设置值
            redisTemplate.opsForValue().set(testKey, testValue, 60, TimeUnit.SECONDS);
            
            // 获取值
            String retrievedValue = (String) redisTemplate.opsForValue().get(testKey);
            
            if (!testValue.equals(retrievedValue)) {
                throw new RuntimeException("值不匹配，期望: " + testValue + "，实际: " + retrievedValue);
            }
            
            // 删除测试数据
            redisTemplate.delete(testKey);
            
            log.info("✅ RedisTemplate 测试通过");
            
        } catch (Exception e) {
            log.error("❌ RedisTemplate 测试失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Redisson 测试
     */
    private void testRedissonClient() {
        log.info("🔧 测试 RedissonClient...");
        
        String testKey = TEST_KEY_PREFIX + "redisson:" + System.currentTimeMillis();
        String testValue = "Redisson测试值_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        
        try {
            // 设置值
            redissonClient.getBucket(testKey).set(testValue, 60, TimeUnit.SECONDS);
            
            // 获取值
            String retrievedValue = (String) redissonClient.getBucket(testKey).get();
            
            if (!testValue.equals(retrievedValue)) {
                throw new RuntimeException("值不匹配，期望: " + testValue + "，实际: " + retrievedValue);
            }
            
            // 删除测试数据
            redissonClient.getBucket(testKey).delete();
            
            log.info("✅ RedissonClient 测试通过");
            
        } catch (Exception e) {
            log.error("❌ RedissonClient 测试失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 增强服务测试
     */
    private void testEnhancedRedisService() {
        log.info("🚀 测试 EnhancedRedisService...");
        
        String testKey = TEST_KEY_PREFIX + "enhanced:" + System.currentTimeMillis();
        String testValue = "Enhanced测试值_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        
        try {
            // 设置值（带重试机制）
            enhancedRedisService.setCacheObject(testKey, testValue, 60L, TimeUnit.SECONDS);
            
            // 获取值（带重试机制）
            String retrievedValue = enhancedRedisService.getCacheObject(testKey);
            
            if (!testValue.equals(retrievedValue)) {
                throw new RuntimeException("值不匹配，期望: " + testValue + "，实际: " + retrievedValue);
            }
            
            // 删除测试数据
            enhancedRedisService.deleteObject(testKey);
            
            log.info("✅ EnhancedRedisService 测试通过");
            
        } catch (Exception e) {
            log.error("❌ EnhancedRedisService 测试失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 健康监控测试
     */
    private void testHealthMonitor() {
        log.info("💊 测试健康监控...");
        
        try {
            // 手动触发健康检查
            boolean isHealthy = redisHealthMonitor.manualHealthCheck();
            
            if (!isHealthy) {
                throw new RuntimeException("健康检查失败");
            }
            
            // 获取连接统计信息
            String stats = redisHealthMonitor.getConnectionStats();
            log.info("📊 Redis 连接统计: \n{}", stats);
            
            log.info("✅ 健康监控测试通过");
            
        } catch (Exception e) {
            log.error("❌ 健康监控测试失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 性能测试
     */
    private void testPerformance() {
        log.info("⚡ 测试性能...");
        
        int testCount = 100;
        String testKeyPrefix = TEST_KEY_PREFIX + "perf:" + System.currentTimeMillis() + ":";
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 批量写入测试
            for (int i = 0; i < testCount; i++) {
                String key = testKeyPrefix + i;
                String value = "性能测试值_" + i;
                enhancedRedisService.setCacheObject(key, value, 60L, TimeUnit.SECONDS);
            }
            
            long writeTime = System.currentTimeMillis() - startTime;
            
            // 批量读取测试
            startTime = System.currentTimeMillis();
            for (int i = 0; i < testCount; i++) {
                String key = testKeyPrefix + i;
                enhancedRedisService.getCacheObject(key);
            }
            
            long readTime = System.currentTimeMillis() - startTime;
            
            // 清理测试数据
            for (int i = 0; i < testCount; i++) {
                String key = testKeyPrefix + i;
                enhancedRedisService.deleteObject(key);
            }
            
            log.info("📈 性能测试结果:");
            log.info("   - 写入 {} 条记录耗时: {}ms，平均: {}ms/条", testCount, writeTime, writeTime / testCount);
            log.info("   - 读取 {} 条记录耗时: {}ms，平均: {}ms/条", testCount, readTime, readTime / testCount);
            
            log.info("✅ 性能测试通过");
            
        } catch (Exception e) {
            log.error("❌ 性能测试失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取 Redis 服务器信息
     */
    public String getRedisServerInfo() {
        try {
            return redisTemplate.execute(connection -> {
                return new String(connection.info());
            });
        } catch (Exception e) {
            log.error("获取 Redis 服务器信息失败: {}", e.getMessage());
            return "获取失败: " + e.getMessage();
        }
    }
}

//package com.yingfei.common.redis.configure;
//
//import com.esotericsoftware.kryo.Kryo;
//import com.esotericsoftware.kryo.io.Input;
//import com.esotericsoftware.kryo.io.Output;
//import de.javakaffee.kryoserializers.JdkProxySerializer;
//import org.objenesis.strategy.StdInstantiatorStrategy;
//import org.springframework.data.redis.serializer.RedisSerializer;
//import org.springframework.data.redis.serializer.SerializationException;
//
//import java.io.ByteArrayInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.lang.reflect.InvocationHandler;
//import java.util.Date;
//import java.util.HashMap;
//
///**
// * Kryo 序列化器，适配 Redis 存储
// */
//public class KryoRedisSerializer<T> implements RedisSerializer<T> {
//
//    // 线程局部变量：Kryo 实例非线程安全，需为每个线程创建独立实例
//    private static final ThreadLocal<Kryo> KRYO_THREAD_LOCAL = ThreadLocal.withInitial(() -> {
//        Kryo kryo = new Kryo();
//        // 配置：关闭严格模式（避免未注册类报错）
//        kryo.setRegistrationRequired(false);
//        // 支持循环引用（防止栈溢出）
//        kryo.setReferences(true);
//        // 优化实例化策略（提升复杂对象序列化性能）
//        kryo.setInstantiatorStrategy(new StdInstantiatorStrategy());
//
//        // 注册常见类型（可选，提升性能）
//        kryo.register(HashMap.class);
//        kryo.register(Date.class);
//        // 支持 JDK 动态代理（如 AOP 代理对象）
//        kryo.register(InvocationHandler.class, new JdkProxySerializer());
//        // 支持 Java 8 时间类型（需引入 kryo-serializers）
//        kryo.register(java.time.LocalDateTime.class);
//        kryo.register(java.time.LocalDate.class);
//
//        return kryo;
//    });
//
//    private final Class<T> type; // 序列化的目标类型
//
//    public KryoRedisSerializer(Class<T> type) {
//        this.type = type;
//    }
//
//    @Override
//    public byte[] serialize(T t) throws SerializationException {
//        if (t == null) {
//            return new byte[0];
//        }
//
//        // 从线程局部变量获取 Kryo 实例
//        Kryo kryo = KRYO_THREAD_LOCAL.get();
//        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
//             Output output = new Output(out)) { // 缓冲输出流，减少 IO 操作
//            kryo.writeObject(output, t); // 序列化对象
//            output.flush();
//            return out.toByteArray();
//        } catch (IOException e) {
//            throw new SerializationException("Kryo 序列化失败：" + t.getClass(), e);
//        } finally {
//            // 清除线程局部变量（可选，避免内存泄漏）
//            KRYO_THREAD_LOCAL.remove();
//        }
//    }
//
//    @Override
//    public T deserialize(byte[] bytes) throws SerializationException {
//        if (bytes == null || bytes.length == 0) {
//            return null;
//        }
//
//        Kryo kryo = KRYO_THREAD_LOCAL.get();
//        try (ByteArrayInputStream in = new ByteArrayInputStream(bytes);
//             Input input = new Input(in)) { // 缓冲输入流
//            return kryo.readObject(input, type); // 反序列化对象
//        } catch (Exception e) {
//            throw new SerializationException("Kryo 反序列化失败：" + type, e);
//        } finally {
//            KRYO_THREAD_LOCAL.remove();
//        }
//    }
//}

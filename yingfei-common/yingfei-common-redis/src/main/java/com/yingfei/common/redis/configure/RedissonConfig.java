package com.yingfei.common.redis.configure;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.config.TransportMode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Redisson 配置类 - 解决 Redis 连接超时问题
 * 所有参数都可通过配置文件进行调整
 *
 * <AUTHOR> Team
 */
@Slf4j
@Configuration
public class RedissonConfig {

    // ========== 基础连接配置 ==========
    @Value("${spring.redis.host:************}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int database;

    // ========== 超时配置 ==========
    @Value("${spring.redis.redisson.connect-timeout:15000}")
    private int connectTimeout;

    @Value("${spring.redis.redisson.timeout:10000}")
    private int commandTimeout;

    @Value("${spring.redis.redisson.retry-attempts:5}")
    private int retryAttempts;

    @Value("${spring.redis.redisson.retry-interval:2000}")
    private int retryInterval;

    // ========== 连接池配置 ==========
    @Value("${spring.redis.redisson.connection-pool-size:50}")
    private int connectionPoolSize;

    @Value("${spring.redis.redisson.connection-minimum-idle-size:10}")
    private int connectionMinimumIdleSize;

    @Value("${spring.redis.redisson.idle-connection-timeout:1800000}")
    private int idleConnectionTimeout;

    @Value("${spring.redis.redisson.connect-timeout-max:3600000}")
    private int connectTimeoutMax;

    // ========== 网络配置 ==========
    @Value("${spring.redis.redisson.tcp-no-delay:true}")
    private boolean tcpNoDelay;

    @Value("${spring.redis.redisson.keep-alive:true}")
    private boolean keepAlive;

    @Value("${spring.redis.redisson.ping-connection-interval:30000}")
    private int pingConnectionInterval;

    @Value("${spring.redis.redisson.ping-timeout:5000}")
    private int pingTimeout;

    // ========== 线程配置 ==========
    @Value("${spring.redis.redisson.netty-threads:0}")
    private int nettyThreads;

    @Value("${spring.redis.redisson.threads:0}")
    private int threads;

    // ========== 分布式锁专用配置 ==========
    @Value("${spring.redis.redisson.lock.connect-timeout:5000}")
    private int lockConnectTimeout;

    @Value("${spring.redis.redisson.lock.timeout:3000}")
    private int lockCommandTimeout;

    @Value("${spring.redis.redisson.lock.retry-attempts:3}")
    private int lockRetryAttempts;

    @Value("${spring.redis.redisson.lock.retry-interval:1000}")
    private int lockRetryInterval;

    @Value("${spring.redis.redisson.lock.connection-pool-size:20}")
    private int lockConnectionPoolSize;

    @Value("${spring.redis.redisson.lock.connection-minimum-idle-size:5}")
    private int lockConnectionMinimumIdleSize;

    @Value("${spring.redis.redisson.lock.idle-connection-timeout:600000}")
    private int lockIdleConnectionTimeout;

    @Value("${spring.redis.redisson.lock.netty-threads:4}")
    private int lockNettyThreads;

    @Value("${spring.redis.redisson.lock.threads:4}")
    private int lockThreads;

    @Bean
    @Primary
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 单机模式配置
        SingleServerConfig singleServerConfig = config.useSingleServer()
                .setAddress("redis://" + redisHost + ":" + redisPort)
                .setDatabase(database);

        // 设置密码（如果有）
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            singleServerConfig.setPassword(redisPassword);
        }

        // 核心超时配置 - 解决 RedisResponseTimeoutException
        singleServerConfig
                .setConnectTimeout(connectTimeout)
                .setTimeout(commandTimeout)
                .setRetryAttempts(retryAttempts)
                .setRetryInterval(retryInterval);

        //  连接池配置 - 提升并发性能
        singleServerConfig
                .setConnectionPoolSize(connectionPoolSize)
                .setConnectionMinimumIdleSize(connectionMinimumIdleSize)
                .setIdleConnectionTimeout(idleConnectionTimeout);

        //  网络配置 - 解决网络包丢失问题
        singleServerConfig
                .setTcpNoDelay(tcpNoDelay)
                .setKeepAlive(keepAlive);

        //  监控和诊断配置
        singleServerConfig
                .setPingConnectionInterval(pingConnectionInterval)
                .setTimeout(pingTimeout);

        //  线程配置 - 解决 nettyThreads 不足问题
        int actualNettyThreads = nettyThreads > 0 ? nettyThreads :
                Math.max(Runtime.getRuntime().availableProcessors() * 2, 8);
        int actualThreads = threads > 0 ? threads :
                Math.max(Runtime.getRuntime().availableProcessors() * 2, 8);

        config
                .setNettyThreads(actualNettyThreads)
                .setThreads(actualThreads);

        //  传输模式配置
        config.setTransportMode(TransportMode.NIO);

        log.info(" Redisson 主客户端配置参数:");
        log.info("   - 连接超时: {}ms", connectTimeout);
        log.info("   - 命令超时: {}ms", commandTimeout);
        log.info("   - 重试次数: {}", retryAttempts);
        log.info("   - 连接池大小: {}", connectionPoolSize);
        log.info("   - Netty线程数: {}", actualNettyThreads);
        log.info("   - 处理线程数: {}", actualThreads);

        return Redisson.create(config);
    }

    /**
     * 创建专门用于分布式锁的 RedissonClient
     * 使用更严格的超时配置确保锁的可靠性
     */
    @Bean("lockRedissonClient")
    public RedissonClient lockRedissonClient() {
        Config config = new Config();

        SingleServerConfig singleServerConfig = config.useSingleServer()
                .setAddress("redis://" + redisHost + ":" + redisPort)
                .setDatabase(database);

        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            singleServerConfig.setPassword(redisPassword);
        }

        // 分布式锁专用配置 - 更短的超时时间，更快的故障检测
        singleServerConfig
                .setConnectTimeout(lockConnectTimeout)
                .setTimeout(lockCommandTimeout)
                .setRetryAttempts(lockRetryAttempts)
                .setRetryInterval(lockRetryInterval)
                .setConnectionPoolSize(lockConnectionPoolSize)
                .setConnectionMinimumIdleSize(lockConnectionMinimumIdleSize)
                .setIdleConnectionTimeout(lockIdleConnectionTimeout)
                .setTcpNoDelay(tcpNoDelay)
                .setKeepAlive(keepAlive);

        config
                .setNettyThreads(lockNettyThreads)
                .setThreads(lockThreads);

        log.info("🔒 Redisson 分布式锁客户端配置参数:");
        log.info("   - 连接超时: {}ms", lockConnectTimeout);
        log.info("   - 命令超时: {}ms", lockCommandTimeout);
        log.info("   - 重试次数: {}", lockRetryAttempts);
        log.info("   - 连接池大小: {}", lockConnectionPoolSize);

        return Redisson.create(config);
    }
}

package com.yingfei.common.redis.configure;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Redisson 配置类 - 解决 Redis 连接超时问题
 * 
 * <AUTHOR> Team
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host:************}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int database;

    @Bean
    @Primary
    public RedissonClient redissonClient() {
        Config config = new Config();
        
        // 单机模式配置
        SingleServerConfig singleServerConfig = config.useSingleServer()
                .setAddress("redis://" + redisHost + ":" + redisPort)
                .setDatabase(database);

        // 设置密码（如果有）
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            singleServerConfig.setPassword(redisPassword);
        }

        // 🔧 核心超时配置 - 解决 RedisResponseTimeoutException
        singleServerConfig
                // 连接超时时间 (毫秒) - 从默认 10000ms 增加到 15000ms
                .setConnectTimeout(15000)
                // 命令等待超时时间 (毫秒) - 从默认 3000ms 增加到 10000ms
                .setTimeout(10000)
                // 命令失败重试次数 - 从默认 3 增加到 5
                .setRetryAttempts(5)
                // 命令重试发送时间间隔 (毫秒) - 从默认 1500ms 增加到 2000ms
                .setRetryInterval(2000);

        // 🚀 连接池配置 - 提升并发性能
        singleServerConfig
                // 连接池大小 - 增加连接数以处理高并发
                .setConnectionPoolSize(50)
                // 最小空闲连接数 - 保持足够的空闲连接
                .setConnectionMinimumIdleSize(10)
                // 空闲连接超时时间 (毫秒) - 30分钟
                .setIdleConnectionTimeout(1800000)
                // 连接最大存活时间 (毫秒) - 1小时
                .setConnectTimeout(3600000);

        // 🔧 网络配置 - 解决网络包丢失问题
        singleServerConfig
                // 发送缓冲区大小 (字节) - 增加缓冲区以减少网络问题
                .setTcpNoDelay(true)
                // 保持连接活跃
                .setKeepAlive(true);

        // 📊 监控和诊断配置
        singleServerConfig
                // 启用 ping 连接检查
                .setPingConnectionInterval(30000)
                // ping 超时时间
                .setPingTimeout(5000);

        // 🧵 线程配置 - 解决 nettyThreads 不足问题
        config
                // Netty 线程数 - 根据 CPU 核心数调整
                .setNettyThreads(Math.max(Runtime.getRuntime().availableProcessors() * 2, 8))
                // 线程数 - 处理 Redis 操作的线程池大小
                .setThreads(Math.max(Runtime.getRuntime().availableProcessors() * 2, 8));

        // 🔄 传输模式配置
        config.setTransportMode(org.redisson.config.TransportMode.NIO);

        return Redisson.create(config);
    }

    /**
     * 创建专门用于分布式锁的 RedissonClient
     * 使用更严格的超时配置确保锁的可靠性
     */
    @Bean("lockRedissonClient")
    public RedissonClient lockRedissonClient() {
        Config config = new Config();
        
        SingleServerConfig singleServerConfig = config.useSingleServer()
                .setAddress("redis://" + redisHost + ":" + redisPort)
                .setDatabase(database);

        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            singleServerConfig.setPassword(redisPassword);
        }

        // 分布式锁专用配置 - 更短的超时时间，更快的故障检测
        singleServerConfig
                .setConnectTimeout(5000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1000)
                .setConnectionPoolSize(20)
                .setConnectionMinimumIdleSize(5)
                .setIdleConnectionTimeout(600000)
                .setTcpNoDelay(true)
                .setKeepAlive(true);

        config
                .setNettyThreads(4)
                .setThreads(4);

        return Redisson.create(config);
    }
}

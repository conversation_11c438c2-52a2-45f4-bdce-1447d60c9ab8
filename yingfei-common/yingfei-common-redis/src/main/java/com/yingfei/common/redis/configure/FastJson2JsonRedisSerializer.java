package com.yingfei.common.redis.configure;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
/**
 * Redis使用FastJson序列化
 */
/**
 * Redis使用FastJson2序列化（不依赖setWriterFeatures方法）
 */
public class FastJson2JsonRedisSerializer<T> implements RedisSerializer<T> {
    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    private static final int BUFFER_SIZE = 256*1024;
    private final Class<T> clazz;
    private final JSONWriter.Feature[] writerFeatures;
    private final JSONReader.Feature[] readerFeatures;

    public FastJson2JsonRedisSerializer(Class<T> clazz) {
        super();
        this.clazz = clazz;

        // 直接定义特性数组，不依赖JSONConfig的set方法
        this.writerFeatures = new JSONWriter.Feature[] {
                JSONWriter.Feature.WriteClassName,
                JSONWriter.Feature.ReferenceDetection,
                JSONWriter.Feature.IgnoreErrorGetter,
                JSONWriter.Feature.WriteBigDecimalAsPlain,
                JSONWriter.Feature.LargeObject
        };

        this.readerFeatures = new JSONReader.Feature[] {
                JSONReader.Feature.SupportAutoType,
                JSONReader.Feature.IgnoreSetNullValue,
                JSONReader.Feature.TrimString
        };
    }

    @Override
    public byte[] serialize(T t) throws SerializationException {
        if (t == null) {
            return new byte[0];
        }

        try (ByteArrayOutputStream out = new ByteArrayOutputStream(BUFFER_SIZE)) {
            // 直接传递特性数组，不依赖JSONConfig配置
            JSON.writeTo(out, t, writerFeatures);
            return out.toByteArray();
        } catch (IOException e) {
            throw new SerializationException("序列化对象失败: " + e.getMessage(), e);
        }
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length <= 0) {
            return null;
        }

        try {
            // 直接使用特性数组进行反序列化
            return JSON.parseObject(bytes, 0, bytes.length, DEFAULT_CHARSET, clazz, readerFeatures);
        } catch (Exception e) {
            throw new SerializationException("反序列化对象失败: " + e.getMessage(), e);
        }
    }
}

package com.yingfei.common.redis.configure;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.beans.factory.annotation.Value;

/**
 * Redis配置类 - 优化连接和序列化配置
 *
 * <AUTHOR> Team
 */
@Slf4j
@Configuration
@EnableCaching
@AutoConfigureBefore(RedisAutoConfiguration.class)
public class RedisConfig extends CachingConfigurerSupport {

    @Value("${spring.redis.host:************}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        log.info("🔧 初始化 RedisTemplate，连接到 Redis 服务器: {}:{}", redisHost, redisPort);

        // 创建 RedisTemplate
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        try {

//        // 创建 FastJson2JsonRedisSerializer，支持任意对象
//        FastJsonRedisSerializer serializer = new FastJsonRedisSerializer(Object.class);
//
//        // 配置 FastJsonConfig
//        FastJsonConfig config = new FastJsonConfig();
//        config.setCharset(StandardCharsets.UTF_8);
//        // 启用 JSONB 格式，减少内存占用
//        config.setJSONB(true);
//        // 序列化配置：优化性能和大数据处理
//        config.setWriterFeatures(
//                JSONWriter.Feature.ReferenceDetection, // 检测循环引用，防止内存溢出
//                JSONWriter.Feature.LargeObject, // 优化大对象序列化
//                JSONWriter.Feature.OptimizedForAscii, // 优化 ASCII 字符
//                JSONWriter.Feature.IgnoreErrorGetter, // 忽略 getter 错误
//                JSONWriter.Feature.WriteNonStringKeyAsString // 非字符串键转为字符串
//                // 未包含 WriteNulls，默认不序列化 null 值
//        );
//        // 反序列化配置 - 支持自动类型识别
//        config.setReaderFeatures(
//                JSONReader.Feature.SupportAutoType
//        );
//        serializer.setFastJsonConfig(config);

        // 设置键序列化器
//        template.setKeySerializer(new StringRedisSerializer());
        // 设置值序列化器
//        template.setValueSerializer(serializer);
//        template.setValueSerializer(serializer);
        // 设置 Hash 键和值的序列化器
//        template.setHashKeySerializer(new StringRedisSerializer());
//        template.setHashValueSerializer(serializer);

            final FastJson2JsonRedisSerializer<Object> objectFastJson2JsonRedisSerializer = new FastJson2JsonRedisSerializer<>(Object.class);

            // 设置键序列化器
            template.setKeySerializer(new StringRedisSerializer());
            // 设置值序列化器
            template.setValueSerializer(objectFastJson2JsonRedisSerializer);
            // 设置 Hash 键和值的序列化器
            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(objectFastJson2JsonRedisSerializer);

            // 初始化 RedisTemplate
            template.afterPropertiesSet();

            log.info("✅ RedisTemplate 初始化成功，使用 FastJson2 序列化器");
            return template;

        } catch (Exception e) {
            log.error("❌ RedisTemplate 初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("Redis 配置初始化失败", e);
        }
    }

//    /**
//     * kryo 序列化
//     */
//    @Bean
//    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
//        RedisTemplate<String, Object> template = new RedisTemplate<>();
//        template.setConnectionFactory(redisConnectionFactory);
//
//        // 设置键序列化器
//        template.setKeySerializer(new StringRedisSerializer());
//        // 设置值序列化器
//        template.setValueSerializer(new KryoRedisSerializer<>());
//        // 设置 Hash 键和值的序列化器
//        template.setHashKeySerializer(new StringRedisSerializer());
//        template.setHashValueSerializer(new KryoRedisSerializer<>());
//
//        template.afterPropertiesSet();
//        return template;
//    }

}

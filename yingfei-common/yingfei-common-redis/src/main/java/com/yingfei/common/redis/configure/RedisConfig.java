package com.yingfei.common.redis.configure;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.beans.factory.annotation.Value;

/**
 * Redis配置类 - 优化连接和序列化配置
 *
 * <AUTHOR> Team
 */
@Slf4j
@Configuration
@EnableCaching
@AutoConfigureBefore(RedisAutoConfiguration.class)
public class RedisConfig extends CachingConfigurerSupport {

    @Value("${spring.redis.host:************}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        log.info(" 初始化 RedisTemplate，连接到 Redis 服务器: {}:{}", redisHost, redisPort);

        // 创建 RedisTemplate
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        try {
            final FastJson2JsonRedisSerializer<Object> objectFastJson2JsonRedisSerializer = new FastJson2JsonRedisSerializer<>(Object.class);
            // 设置键序列化器
            template.setKeySerializer(new StringRedisSerializer());
            // 设置值序列化器
            template.setValueSerializer(objectFastJson2JsonRedisSerializer);
            // 设置 Hash 键和值的序列化器
            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(objectFastJson2JsonRedisSerializer);
            // 初始化 RedisTemplate
            template.afterPropertiesSet();

            log.info(" RedisTemplate 初始化成功，使用 FastJson2 序列化器");
            return template;

        } catch (Exception e) {
            log.error(" RedisTemplate 初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("Redis 配置初始化失败", e);
        }
    }

//    /**
//     * kryo 序列化
//     */
//    @Bean
//    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
//        RedisTemplate<String, Object> template = new RedisTemplate<>();
//        template.setConnectionFactory(redisConnectionFactory);
//
//        // 设置键序列化器
//        template.setKeySerializer(new StringRedisSerializer());
//        // 设置值序列化器
//        template.setValueSerializer(new KryoRedisSerializer<>());
//        // 设置 Hash 键和值的序列化器
//        template.setHashKeySerializer(new StringRedisSerializer());
//        template.setHashValueSerializer(new KryoRedisSerializer<>());
//
//        template.afterPropertiesSet();
//        return template;
//    }

}

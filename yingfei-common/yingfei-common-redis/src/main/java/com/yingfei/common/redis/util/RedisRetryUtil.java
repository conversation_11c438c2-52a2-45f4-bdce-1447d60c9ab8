package com.yingfei.common.redis.util;

import lombok.extern.slf4j.Slf4j;
import org.redisson.client.RedisResponseTimeoutException;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.RedisConnectionFailureException;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis 重试工具类
 * 提供智能重试机制，处理各种 Redis 连接异常
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class RedisRetryUtil {

    // 默认重试配置
    private static final int DEFAULT_MAX_RETRIES = 3;
    private static final long DEFAULT_RETRY_DELAY_MS = 1000;
    private static final double RETRY_DELAY_MULTIPLIER = 1.5;

    /**
     * 执行 Redis 操作，带重试机制
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName) {
        return executeWithRetry(operation, operationName, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_DELAY_MS);
    }

    /**
     * 执行 Redis 操作，带自定义重试配置
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param maxRetries 最大重试次数
     * @param initialDelayMs 初始延迟时间（毫秒）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName, 
                                       int maxRetries, long initialDelayMs) {
        Exception lastException = null;
        long delayMs = initialDelayMs;

        for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                T result = operation.get();
                
                // 如果之前有失败，记录恢复日志
                if (attempt > 1) {
                    log.info("✅ Redis 操作 [{}] 在第 {} 次尝试后成功恢复", operationName, attempt);
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                
                // 判断是否应该重试
                if (!shouldRetry(e) || attempt > maxRetries) {
                    break;
                }
                
                log.warn("⚠️ Redis 操作 [{}] 第 {} 次尝试失败: {}，{}ms 后重试", 
                        operationName, attempt, e.getMessage(), delayMs);
                
                // 等待后重试
                try {
                    TimeUnit.MILLISECONDS.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程被中断", ie);
                }
                
                // 指数退避：每次重试延迟时间递增
                delayMs = (long) (delayMs * RETRY_DELAY_MULTIPLIER);
            }
        }

        // 所有重试都失败了
        log.error("❌ Redis 操作 [{}] 在 {} 次尝试后最终失败", operationName, maxRetries + 1);
        throw new RuntimeException("Redis 操作失败: " + operationName, lastException);
    }

    /**
     * 执行无返回值的 Redis 操作，带重试机制
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     */
    public static void executeWithRetry(Runnable operation, String operationName) {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, operationName);
    }

    /**
     * 判断异常是否应该重试
     * 
     * @param exception 异常
     * @return 是否应该重试
     */
    private static boolean shouldRetry(Exception exception) {
        // Redis 响应超时异常 - 应该重试
        if (exception instanceof RedisResponseTimeoutException) {
            return true;
        }
        
        // Redis 连接失败异常 - 应该重试
        if (exception instanceof RedisConnectionFailureException) {
            return true;
        }
        
        // Spring Data Redis 数据访问异常 - 应该重试
        if (exception instanceof DataAccessException) {
            return true;
        }
        
        // 网络相关异常 - 应该重试
        if (exception.getCause() instanceof java.net.SocketTimeoutException ||
            exception.getCause() instanceof java.net.ConnectException ||
            exception.getCause() instanceof java.io.IOException) {
            return true;
        }
        
        // 包含特定关键词的异常消息 - 应该重试
        String message = exception.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("timeout") ||
                lowerMessage.contains("connection") ||
                lowerMessage.contains("network") ||
                lowerMessage.contains("broken pipe") ||
                lowerMessage.contains("connection reset")) {
                return true;
            }
        }
        
        // 其他异常不重试
        return false;
    }

    /**
     * 获取异常的友好描述
     * 
     * @param exception 异常
     * @return 友好描述
     */
    public static String getExceptionDescription(Exception exception) {
        if (exception instanceof RedisResponseTimeoutException) {
            return "Redis 服务器响应超时";
        }
        
        if (exception instanceof RedisConnectionFailureException) {
            return "Redis 连接失败";
        }
        
        if (exception instanceof DataAccessException) {
            return "Redis 数据访问异常";
        }
        
        if (exception.getCause() instanceof java.net.SocketTimeoutException) {
            return "网络连接超时";
        }
        
        if (exception.getCause() instanceof java.net.ConnectException) {
            return "无法连接到 Redis 服务器";
        }
        
        return exception.getMessage() != null ? exception.getMessage() : exception.getClass().getSimpleName();
    }

    /**
     * 创建重试配置构建器
     */
    public static class RetryConfigBuilder {
        private int maxRetries = DEFAULT_MAX_RETRIES;
        private long initialDelayMs = DEFAULT_RETRY_DELAY_MS;
        private double delayMultiplier = RETRY_DELAY_MULTIPLIER;

        public RetryConfigBuilder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public RetryConfigBuilder initialDelay(long delayMs) {
            this.initialDelayMs = delayMs;
            return this;
        }

        public RetryConfigBuilder delayMultiplier(double multiplier) {
            this.delayMultiplier = multiplier;
            return this;
        }

        public <T> T execute(Supplier<T> operation, String operationName) {
            return executeWithRetry(operation, operationName, maxRetries, initialDelayMs);
        }

        public void execute(Runnable operation, String operationName) {
            executeWithRetry(operation, operationName);
        }
    }

    /**
     * 创建重试配置构建器
     */
    public static RetryConfigBuilder builder() {
        return new RetryConfigBuilder();
    }
}

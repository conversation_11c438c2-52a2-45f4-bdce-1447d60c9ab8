package com.yingfei.common.redis.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * Redis 连接健康监控组件
 * 定期检查 Redis 连接状态，及时发现和报告连接问题
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class RedisHealthMonitor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    private static final String HEALTH_CHECK_KEY = "redis:health:check";
    private static final String HEALTH_CHECK_VALUE = "OK";
    
    // 连续失败次数计数器
    private int consecutiveFailures = 0;
    private static final int MAX_CONSECUTIVE_FAILURES = 3;

    @PostConstruct
    public void init() {
        log.info("🔍 Redis 健康监控组件已启动");
        // 启动时立即执行一次健康检查
        performHealthCheck();
    }

    /**
     * 定期健康检查 - 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000)
    public void scheduledHealthCheck() {
        performHealthCheck();
    }

    /**
     * 执行 Redis 健康检查
     */
    public void performHealthCheck() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        try {
            // 检查 RedisTemplate 连接
            checkRedisTemplate();
            
            // 检查 Redisson 连接
            checkRedissonClient();
            
            // 重置失败计数器
            if (consecutiveFailures > 0) {
                log.info("✅ Redis 连接已恢复正常，重置失败计数器");
                consecutiveFailures = 0;
            }
            
            log.debug("✅ Redis 健康检查通过 [{}]", timestamp);
            
        } catch (Exception e) {
            consecutiveFailures++;
            log.error("❌ Redis 健康检查失败 [{}] - 连续失败次数: {}/{}", 
                    timestamp, consecutiveFailures, MAX_CONSECUTIVE_FAILURES, e);
            
            // 连续失败达到阈值时发出警告
            if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                handleCriticalFailure(e);
            }
        }
    }

    /**
     * 检查 RedisTemplate 连接
     */
    private void checkRedisTemplate() {
        try {
            // 执行简单的 ping 操作
            String result = redisTemplate.execute(connection -> {
                return connection.ping();
            });
            
            if (!"PONG".equals(result)) {
                throw new RuntimeException("RedisTemplate ping 返回异常结果: " + result);
            }
            
            // 执行读写测试
            String testKey = HEALTH_CHECK_KEY + ":template:" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(testKey, HEALTH_CHECK_VALUE, 10, TimeUnit.SECONDS);
            String value = (String) redisTemplate.opsForValue().get(testKey);
            
            if (!HEALTH_CHECK_VALUE.equals(value)) {
                throw new RuntimeException("RedisTemplate 读写测试失败");
            }
            
            // 清理测试数据
            redisTemplate.delete(testKey);
            
        } catch (Exception e) {
            throw new RuntimeException("RedisTemplate 健康检查失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查 Redisson 连接
     */
    private void checkRedissonClient() {
        try {
            // 检查 Redisson 连接状态
            if (redissonClient.isShutdown() || redissonClient.isShuttingDown()) {
                throw new RuntimeException("Redisson 客户端已关闭");
            }
            
            // 执行简单的操作测试
            String testKey = HEALTH_CHECK_KEY + ":redisson:" + System.currentTimeMillis();
            redissonClient.getBucket(testKey).set(HEALTH_CHECK_VALUE, 10, TimeUnit.SECONDS);
            String value = (String) redissonClient.getBucket(testKey).get();
            
            if (!HEALTH_CHECK_VALUE.equals(value)) {
                throw new RuntimeException("Redisson 读写测试失败");
            }
            
            // 清理测试数据
            redissonClient.getBucket(testKey).delete();
            
        } catch (Exception e) {
            throw new RuntimeException("Redisson 健康检查失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理严重故障
     */
    private void handleCriticalFailure(Exception e) {
        log.error("🚨 Redis 连接严重故障！连续失败 {} 次，请立即检查：", consecutiveFailures);
        log.error("   1. Redis 服务器 192.168.2.87:6379 是否正常运行");
        log.error("   2. 网络连接是否正常");
        log.error("   3. 防火墙设置是否阻止连接");
        log.error("   4. Redis 服务器负载是否过高");
        log.error("   错误详情: {}", e.getMessage());
        
        // 这里可以添加告警通知逻辑，如发送邮件、短信等
        // sendAlert("Redis连接严重故障", e.getMessage());
    }

    /**
     * 获取 Redis 连接统计信息
     */
    public String getConnectionStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("Redis 连接统计信息:\n");
            stats.append("- 连续失败次数: ").append(consecutiveFailures).append("/").append(MAX_CONSECUTIVE_FAILURES).append("\n");
            stats.append("- 检查时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            
            // 尝试获取 Redis 信息
            String info = redisTemplate.execute(connection -> {
                return new String(connection.info());
            });
            
            if (info != null && info.contains("redis_version")) {
                String[] lines = info.split("\n");
                for (String line : lines) {
                    if (line.startsWith("redis_version:") || 
                        line.startsWith("connected_clients:") || 
                        line.startsWith("used_memory_human:") ||
                        line.startsWith("total_commands_processed:")) {
                        stats.append("- ").append(line.trim()).append("\n");
                    }
                }
            }
            
            return stats.toString();
            
        } catch (Exception e) {
            return "获取 Redis 统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发健康检查
     */
    public boolean manualHealthCheck() {
        try {
            performHealthCheck();
            return true;
        } catch (Exception e) {
            log.error("手动健康检查失败: {}", e.getMessage(), e);
            return false;
        }
    }
}

package com.yingfei.common.redis.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * Redis 连接健康监控组件
 * 定期检查 Redis 连接状态，及时发现和报告连接问题
 * 所有监控参数都可通过配置文件进行调整
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class RedisHealthMonitor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    // ========== 监控配置参数 ==========
    @Value("${spring.redis.monitor.health-check-key:redis:health:check}")
    private String healthCheckKey;

    @Value("${spring.redis.monitor.health-check-value:OK}")
    private String healthCheckValue;

    @Value("${spring.redis.monitor.max-consecutive-failures:3}")
    private int maxConsecutiveFailures;

    @Value("${spring.redis.monitor.test-data-ttl:10}")
    private int testDataTtl;

    @Value("${spring.redis.monitor.enabled:true}")
    private boolean monitorEnabled;

    // 连续失败次数计数器
    private int consecutiveFailures = 0;

    @PostConstruct
    public void init() {
        if (monitorEnabled) {
            log.info("🔍 Redis 健康监控组件已启动");
            log.info("   - 健康检查键: {}", healthCheckKey);
            log.info("   - 最大连续失败次数: {}", maxConsecutiveFailures);
            log.info("   - 测试数据TTL: {}秒", testDataTtl);
            // 启动时立即执行一次健康检查
            performHealthCheck();
        } else {
            log.info("⏸️ Redis 健康监控已禁用");
        }
    }

    /**
     * 定期健康检查 - 通过配置文件控制执行间隔
     * 默认每30秒执行一次：${spring.redis.monitor.check-interval:30000}
     */
    @Scheduled(fixedRateString = "${spring.redis.monitor.check-interval:30000}")
    public void scheduledHealthCheck() {
        if (monitorEnabled) {
            performHealthCheck();
        }
    }

    /**
     * 执行 Redis 健康检查
     */
    public void performHealthCheck() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        try {
            // 检查 RedisTemplate 连接
            checkRedisTemplate();
            
            // 检查 Redisson 连接
            checkRedissonClient();
            
            // 重置失败计数器
            if (consecutiveFailures > 0) {
                log.info("✅ Redis 连接已恢复正常，重置失败计数器");
                consecutiveFailures = 0;
            }
            
            log.debug("✅ Redis 健康检查通过 [{}]", timestamp);
            
        } catch (Exception e) {
            consecutiveFailures++;
            log.error("❌ Redis 健康检查失败 [{}] - 连续失败次数: {}/{}", 
                    timestamp, consecutiveFailures, MAX_CONSECUTIVE_FAILURES, e);
            
            // 连续失败达到阈值时发出警告
            if (consecutiveFailures >= maxConsecutiveFailures) {
                handleCriticalFailure(e);
            }
        }
    }

    /**
     * 检查 RedisTemplate 连接
     */
    private void checkRedisTemplate() {
        try {
            // 执行简单的 ping 操作
            String result = redisTemplate.execute(connection -> {
                return connection.ping();
            });
            
            if (!"PONG".equals(result)) {
                throw new RuntimeException("RedisTemplate ping 返回异常结果: " + result);
            }
            
            // 执行读写测试
            String testKey = healthCheckKey + ":template:" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(testKey, healthCheckValue, testDataTtl, TimeUnit.SECONDS);
            String value = (String) redisTemplate.opsForValue().get(testKey);

            if (!healthCheckValue.equals(value)) {
                throw new RuntimeException("RedisTemplate 读写测试失败");
            }
            
            // 清理测试数据
            redisTemplate.delete(testKey);
            
        } catch (Exception e) {
            throw new RuntimeException("RedisTemplate 健康检查失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查 Redisson 连接
     */
    private void checkRedissonClient() {
        try {
            // 检查 Redisson 连接状态
            if (redissonClient.isShutdown() || redissonClient.isShuttingDown()) {
                throw new RuntimeException("Redisson 客户端已关闭");
            }
            
            // 执行简单的操作测试
            String testKey = healthCheckKey + ":redisson:" + System.currentTimeMillis();
            redissonClient.getBucket(testKey).set(healthCheckValue, testDataTtl, TimeUnit.SECONDS);
            String value = (String) redissonClient.getBucket(testKey).get();

            if (!healthCheckValue.equals(value)) {
                throw new RuntimeException("Redisson 读写测试失败");
            }
            
            // 清理测试数据
            redissonClient.getBucket(testKey).delete();
            
        } catch (Exception e) {
            throw new RuntimeException("Redisson 健康检查失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理严重故障
     */
    private void handleCriticalFailure(Exception e) {
        log.error("🚨 Redis 连接严重故障！连续失败 {} 次，请立即检查：", consecutiveFailures);
        log.error("   1. Redis 服务器 192.168.2.87:6379 是否正常运行");
        log.error("   2. 网络连接是否正常");
        log.error("   3. 防火墙设置是否阻止连接");
        log.error("   4. Redis 服务器负载是否过高");
        log.error("   错误详情: {}", e.getMessage());
        
        // 这里可以添加告警通知逻辑，如发送邮件、短信等
        // sendAlert("Redis连接严重故障", e.getMessage());
    }

    /**
     * 获取 Redis 连接统计信息
     */
    public String getConnectionStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("Redis 连接统计信息:\n");
            stats.append("- 连续失败次数: ").append(consecutiveFailures).append("/").append(maxConsecutiveFailures).append("\n");
            stats.append("- 检查时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            
            // 尝试获取 Redis 信息
            String info = redisTemplate.execute(connection -> {
                return new String(connection.info());
            });
            
            if (info != null && info.contains("redis_version")) {
                String[] lines = info.split("\n");
                for (String line : lines) {
                    if (line.startsWith("redis_version:") || 
                        line.startsWith("connected_clients:") || 
                        line.startsWith("used_memory_human:") ||
                        line.startsWith("total_commands_processed:")) {
                        stats.append("- ").append(line.trim()).append("\n");
                    }
                }
            }
            
            return stats.toString();
            
        } catch (Exception e) {
            return "获取 Redis 统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发健康检查
     */
    public boolean manualHealthCheck() {
        try {
            performHealthCheck();
            return true;
        } catch (Exception e) {
            log.error("手动健康检查失败: {}", e.getMessage(), e);
            return false;
        }
    }
}

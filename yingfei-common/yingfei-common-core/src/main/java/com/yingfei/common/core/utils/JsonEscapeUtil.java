package com.yingfei.common.core.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * JSON 字符串转义工具类
 * 提供各种场景下的字符串转义功能，防止序列化异常
 *
 * <AUTHOR>
 */
public class JsonEscapeUtil {

    /**
     * 预定义的转义字符映射表
     */
    private static final Map<Character, String> ESCAPE_MAP = new HashMap<>();

    static {
        // HTML/XML 特殊字符
        ESCAPE_MAP.put('<', "\\u003c");
        ESCAPE_MAP.put('>', "\\u003e");
        ESCAPE_MAP.put('&', "\\u0026");
        ESCAPE_MAP.put('"', "\\u0022");
        ESCAPE_MAP.put('\'', "\\u0027");

        // JSON 控制字符
        ESCAPE_MAP.put('\b', "\\u0008");  // 退格
        ESCAPE_MAP.put('\f', "\\u000c");  // 换页
        ESCAPE_MAP.put('\n', "\\u000a");  // 换行
        ESCAPE_MAP.put('\r', "\\u000d");  // 回车
        ESCAPE_MAP.put('\t', "\\u0009");  // 制表符
        ESCAPE_MAP.put('\\', "\\u005c");  // 反斜杠
        ESCAPE_MAP.put('/', "\\u002f");   // 正斜杠
    }

    /**
     * 转义字符串中的特殊字符（基础版本）
     * 只转义最常见的 HTML/XML 特殊字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    public static String escapeBasic(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        return str.replace("<", "\\u003c")
                .replace(">", "\\u003e")
                .replace("&", "\\u0026")
                .replace("\"", "\\u0022")
                .replace("'", "\\u0027");
    }

    /**
     * 转义字符串中的特殊字符（完整版本）
     * 转义所有可能导致 JSON 序列化问题的字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    public static String escapeFull(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        StringBuilder sb = new StringBuilder(str.length() + 16);

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);

            // 检查预定义的转义字符
            String escaped = ESCAPE_MAP.get(c);
            if (escaped != null) {
                sb.append(escaped);
            } else if (c < 0x20 || c > 0x7E) {
                // 转义控制字符和非 ASCII 字符
                sb.append(String.format("\\u%04x", (int) c));
            } else {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 转义字符串中的特殊字符（性能优化版本）
     * 只有在检测到特殊字符时才进行转义处理
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    public static String escapeOptimized(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        // 快速检查是否包含需要转义的字符
        boolean needsEscape = false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (ESCAPE_MAP.containsKey(c) || c < 0x20) {
                needsEscape = true;
                break;
            }
        }

        // 如果不需要转义，直接返回原字符串
        if (!needsEscape) {
            return str;
        }

        // 需要转义时才创建 StringBuilder
        StringBuilder sb = new StringBuilder(str.length() + 16);

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);

            String escaped = ESCAPE_MAP.get(c);
            if (escaped != null) {
                sb.append(escaped);
            } else if (c < 0x20) {
                sb.append(String.format("\\u%04x", (int) c));
            } else {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 反转义字符串中的 Unicode 转义序列
     *
     * @param str 包含转义序列的字符串
     * @return 反转义后的字符串
     */
    public static String unescape(String str) {
        if (str == null || str.isEmpty() || !str.contains("\\u")) {
            return str;
        }

        StringBuilder sb = new StringBuilder(str.length());

        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) == '\\' && i + 5 < str.length() && str.charAt(i + 1) == 'u') {
                try {
                    // 解析 Unicode 转义序列
                    String hex = str.substring(i + 2, i + 6);
                    int codePoint = Integer.parseInt(hex, 16);
                    sb.append((char) codePoint);
                    i += 5; // 跳过整个转义序列
                } catch (NumberFormatException e) {
                    // 如果不是有效的 Unicode 转义序列，保持原样
                    sb.append(str.charAt(i));
                }
            } else {
                sb.append(str.charAt(i));
            }
        }

        return sb.toString();
    }

    private static final Map<String, Character> ENTITY_MAP = new HashMap<>();

    static {
        ENTITY_MAP.put("gt", '>');
        ENTITY_MAP.put("lt", '<');
        ENTITY_MAP.put("amp", '&');
        ENTITY_MAP.put("quot", '"');
        ENTITY_MAP.put("apos", '\'');
    }

    public static String unescapeHtml(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        StringBuilder sb = new StringBuilder(str.length());
        int i = 0;
        int len = str.length();
        while (i < len) {
            if (str.charAt(i) == '&') {
                int j = str.indexOf(';', i);
                if (j > i + 1) {
                    String entity = str.substring(i + 1, j);
                    Character ch = ENTITY_MAP.get(entity);
                    if (ch != null) {
                        sb.append(ch);
                        i = j + 1;
                        continue;
                    }
                }
            }
            sb.append(str.charAt(i));
            i++;
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        String str = "&gt;USL";
        System.out.println(unescapeHtml(str)); // Should output >USL
    }

    /**
     * 检查字符串是否包含需要转义的特殊字符
     *
     * @param str 要检查的字符串
     * @return 如果包含特殊字符返回 true，否则返回 false
     */
    public static boolean containsSpecialCharacters(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (ESCAPE_MAP.containsKey(c) || c < 0x20) {
                return true;
            }
        }

        return false;
    }

    /**
     * 批量转义字符串数组
     *
     * @param strings 字符串数组
     * @return 转义后的字符串数组
     */
    public static String[] escapeArray(String[] strings) {
        if (strings == null) {
            return null;
        }

        String[] result = new String[strings.length];
        for (int i = 0; i < strings.length; i++) {
            result[i] = escapeOptimized(strings[i]);
        }

        return result;
    }

}

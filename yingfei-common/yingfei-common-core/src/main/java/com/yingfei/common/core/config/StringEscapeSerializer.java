package com.yingfei.common.core.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.util.List;

/**
 * 字符串转义序列化器
 * 专门用于处理 List<List<Object>> 类型数据中的特殊字符转义
 * 防止 Jackson 序列化时因特殊字符（如 <、>、&）导致的异常
 *
 * <AUTHOR>
 */
public class StringEscapeSerializer extends JsonSerializer<List<List<Object>>> {

    /**
     * 序列化方法，对字符串类型的元素进行特殊字符转义
     *
     * @param value 要序列化的二维列表
     * @param gen JSON生成器
     * @param serializers 序列化提供者
     * @throws IOException 序列化异常
     */
    @Override
    public void serialize(List<List<Object>> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartArray();
        for (List<Object> innerList : value) {
            if (innerList == null) {
                gen.writeNull();
                continue;
            }

            gen.writeStartArray();
            for (Object element : innerList) {
                if (element != null) {
                    if (element instanceof String) {
                        // 对字符串类型进行特殊字符转义
                        String escapedStr = escapeSpecialCharacters((String) element);
                        gen.writeString(escapedStr);
                    } else {
                        // 非字符串类型直接写入
                        gen.writeObject(element);
                    }
                } else {
                    gen.writeNull();
                }
            }
            gen.writeEndArray();
        }
        gen.writeEndArray();
    }

    /**
     * 转义特殊字符，防止 JSON 序列化异常
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeSpecialCharacters(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        // 使用 StringBuilder 提高性能
        StringBuilder sb = new StringBuilder(str.length() + 16);

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            switch (c) {
                case '<':
                    sb.append("\\u003c");
                    break;
                case '>':
                    sb.append("\\u003e");
                    break;
                case '&':
                    sb.append("\\u0026");
                    break;
                case '"':
                    sb.append("\\u0022");
                    break;
                case '\'':
                    sb.append("\\u0027");
                    break;
                case '\b':
                    sb.append("\\u0008");
                    break;
                case '\f':
                    sb.append("\\u000c");
                    break;
                case '\n':
                    sb.append("\\u000a");
                    break;
                case '\r':
                    sb.append("\\u000d");
                    break;
                case '\t':
                    sb.append("\\u0009");
                    break;
                default:
                    // 对于控制字符进行转义
                    if (c < 0x20) {
                        sb.append(String.format("\\u%04x", (int) c));
                    } else {
                        sb.append(c);
                    }
                    break;
            }
        }

        return sb.toString();
    }
}

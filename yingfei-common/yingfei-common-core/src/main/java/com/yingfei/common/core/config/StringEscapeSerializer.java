package com.yingfei.common.core.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.yingfei.common.core.utils.JsonEscapeUtil;

import java.io.IOException;
import java.util.List;

/**
 * 字符串转义序列化器
 * 专门用于处理 List<List<Object>> 类型数据中的特殊字符转义
 * 防止 Jackson 序列化时因特殊字符（如 <、>、&）导致的异常
 *
 * <AUTHOR>
 */
public class StringEscapeSerializer extends JsonSerializer<List<List<Object>>> {

    /**
     * 序列化方法，对字符串类型的元素进行特殊字符转义
     *
     * @param value 要序列化的二维列表
     * @param gen JSON生成器
     * @param serializers 序列化提供者
     * @throws IOException 序列化异常
     */
    @Override
    public void serialize(List<List<Object>> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartArray();
        for (List<Object> innerList : value) {
            if (innerList == null) {
                gen.writeNull();
                continue;
            }

            gen.writeStartArray();
            for (Object element : innerList) {
                if (element != null) {
                    if (element instanceof String) {
                        // 对字符串类型进行特殊字符转义
                        String escapedStr = JsonEscapeUtil.escapeBasic((String) element);
                        gen.writeString(escapedStr);
                    } else {
                        // 非字符串类型直接写入
                        gen.writeObject(element);
                    }
                } else {
                    gen.writeNull();
                }
            }
            gen.writeEndArray();
        }
        gen.writeEndArray();
    }


}

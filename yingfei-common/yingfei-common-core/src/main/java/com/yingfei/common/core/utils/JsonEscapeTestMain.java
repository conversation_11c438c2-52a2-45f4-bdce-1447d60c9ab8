//package com.yingfei.common.core.utils;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.module.SimpleModule;
//import com.yingfei.common.core.config.StringEscapeSerializer;
//
//import java.util.Arrays;
//import java.util.List;
//
///**
// * JSON 转义功能测试主类
// * 用于验证特殊字符转义功能是否正常工作
// *
// * <AUTHOR>
// */
//public class JsonEscapeTestMain {
//
//    public static void main(String[] args) {
//        System.out.println("=== JSON 特殊字符转义功能测试 ===\n");
//
//        // 测试 JsonEscapeUtil 工具类
//        testJsonEscapeUtil();
//
//        // 测试 StringEscapeSerializer 序列化器
//        testStringEscapeSerializer();
//
//        System.out.println("=== 测试完成 ===");
//    }
//
//    /**
//     * 测试 JsonEscapeUtil 工具类
//     */
//    private static void testJsonEscapeUtil() {
//        System.out.println("1. 测试 JsonEscapeUtil 工具类:");
//
//        // 测试基础转义
//        String[] testStrings = {
//            "<LSL",
//            ">USL",
//            "实际值>USL",
//            "实际值<LSL",
//            "A & B",
//            "\"quoted\"",
//            "'single'",
//            "line\nbreak",
//            "tab\there"
//        };
//
//        System.out.println("   基础转义测试:");
//        for (String str : testStrings) {
//            String escaped = JsonEscapeUtil.escapeBasic(str);
//            System.out.println("   原始: " + str + " -> 转义: " + escaped);
//        }
//
//        System.out.println("\n   性能优化版本测试:");
//        for (String str : testStrings) {
//            String escaped = JsonEscapeUtil.escapeOptimized(str);
//            System.out.println("   原始: " + str + " -> 转义: " + escaped);
//        }
//
//        // 测试反转义
//        System.out.println("\n   反转义测试:");
//        String escapedString = "\\u003cLSL";
//        String unescaped = JsonEscapeUtil.unescape(escapedString);
//        System.out.println("   转义: " + escapedString + " -> 反转义: " + unescaped);
//
//        // 测试特殊字符检测
//        System.out.println("\n   特殊字符检测测试:");
//        System.out.println("   '<LSL' 包含特殊字符: " + JsonEscapeUtil.containsSpecialCharacters("<LSL"));
//        System.out.println("   'normal text' 包含特殊字符: " + JsonEscapeUtil.containsSpecialCharacters("normal text"));
//
//        System.out.println();
//    }
//
//    /**
//     * 测试 StringEscapeSerializer 序列化器
//     */
//    private static void testStringEscapeSerializer() {
//        System.out.println("2. 测试 StringEscapeSerializer 序列化器:");
//
//        try {
//            // 创建 ObjectMapper 并注册自定义序列化器
//            ObjectMapper objectMapper = new ObjectMapper();
//            SimpleModule module = new SimpleModule();
//            module.addSerializer(Listclass, new StringEscapeSerializer());
//            objectMapper.registerModule(module);
//
//            // 创建测试数据（模拟 SPC 系统的实际数据）
//            List<List<Object>> testData = Arrays.asList(
//                Arrays.asList(">USL", null, 3, 0.75),
//                Arrays.asList("<LSL", null, 1, 0.25),
//                Arrays.asList("实际值>USL", "实际值<LSL", "实际值总数", "实际PPM"),
//                Arrays.asList("预期值>USL", "预期值<LSL", "预期值总数", "预期PPM"),
//                Arrays.asList("A & B", "test\"quote", 123, 45.67)
//            );
//
//            // 序列化
//            String json = objectMapper.writeValueAsString(testData);
//
//            System.out.println("   序列化结果:");
//            System.out.println("   " + json);
//
//            // 验证特殊字符是否被正确转义
//            System.out.println("\n   转义验证:");
//            System.out.println("   包含 \\u003eUSL: " + json.contains("\\u003eUSL"));
//            System.out.println("   包含 \\u003cLSL: " + json.contains("\\u003cLSL"));
//            System.out.println("   包含 \\u0026: " + json.contains("\\u0026"));
//            System.out.println("   包含 \\u0022: " + json.contains("\\u0022"));
//
//            // 验证不包含未转义的特殊字符
//            System.out.println("   不包含未转义的 >USL: " + !json.contains(">USL"));
//            System.out.println("   不包含未转义的 <LSL: " + !json.contains("<LSL"));
//
//        } catch (JsonProcessingException e) {
//            System.err.println("   序列化测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//
//        System.out.println();
//    }
//}

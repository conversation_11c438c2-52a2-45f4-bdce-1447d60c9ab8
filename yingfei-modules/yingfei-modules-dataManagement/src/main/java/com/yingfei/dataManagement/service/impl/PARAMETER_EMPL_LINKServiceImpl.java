package com.yingfei.dataManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.mapper.PARAMETER_EMPL_LINKMapper;
import com.yingfei.dataManagement.service.PARAMETER_EMPL_LINKService;
import com.yingfei.entity.domain.PARAMETER_EMPL_LINK;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PARAMETER_EMPL_LINKServiceImpl extends ServiceImpl<PARAMETER_EMPL_LINKMapper, PARAMETER_EMPL_LINK> implements PARAMETER_EMPL_LINKService {

    @Override
    @Transactional
    public void add(List<PARAMETER_EMPL_LINK> parameterEmplLinkList, Long fPrst) {
        /*先删除*/
        LambdaQueryWrapper<PARAMETER_EMPL_LINK> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PARAMETER_EMPL_LINK::getF_PARAMETER_ID, fPrst);
        baseMapper.delete(queryWrapper);
        for (PARAMETER_EMPL_LINK parameterEmplLink : parameterEmplLinkList) {
            parameterEmplLink.setF_PARAMETER_ID(fPrst);
            parameterEmplLink.setF_CRUE(SecurityUtils.getUserId());
            parameterEmplLink.setF_EDUE(SecurityUtils.getUserId());
        }
        saveBatch(parameterEmplLinkList);
    }

    @Override
    public List<PARAMETER_EMPL_LINK> findByNeEmplAndRole(Long userId, Long roleId) {
        LambdaQueryWrapper<PARAMETER_EMPL_LINK> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PARAMETER_EMPL_LINK::getF_DATA_ID, userId).or().eq(PARAMETER_EMPL_LINK::getF_DATA_ID, roleId);
        List<PARAMETER_EMPL_LINK> parameterEmplLinkList = baseMapper.selectList(wrapper);
        return baseMapper.findByNeEmplAndRole(userId, roleId, parameterEmplLinkList.stream().map(PARAMETER_EMPL_LINK::getF_PARAMETER_ID).collect(Collectors.toList()));
    }

    @Override
    public List<PARAMETER_EMPL_LINK> findByParameterId(Long id) {
        LambdaQueryWrapper<PARAMETER_EMPL_LINK> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PARAMETER_EMPL_LINK::getF_PARAMETER_ID, id);
        return baseMapper.selectList(queryWrapper);
    }
}

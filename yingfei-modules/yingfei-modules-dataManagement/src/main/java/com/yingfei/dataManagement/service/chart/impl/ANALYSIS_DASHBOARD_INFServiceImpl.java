package com.yingfei.dataManagement.service.chart.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.ANALYSIS_DASHBOARD_INFMapper;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_TEMPLATE_INF;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_INF_DTO;
import com.yingfei.entity.vo.ANALYSIS_DASHBOARD_INF_VO;
import com.yingfei.system.api.RemoteMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 针对表【ANALYSIS_DASHBOARD_INF(分析页面关联表)】的数据库操作Service实现
 * @createDate 2024-09-04 16:01:11
 */
@Service
public class ANALYSIS_DASHBOARD_INFServiceImpl extends ServiceImpl<ANALYSIS_DASHBOARD_INFMapper, ANALYSIS_DASHBOARD_INF>
        implements ANALYSIS_DASHBOARD_INFService {

    @Resource
    private RemoteMenuService remoteMenuService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;

    @Override
    public long getTotal(ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        return baseMapper.getTotal(analysisDashboardInfVo);
    }

    @Override
    public List<ANALYSIS_DASHBOARD_INF_DTO> getList(ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        return baseMapper.getList(analysisDashboardInfVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        /*判断菜单是否关联*/
        LambdaQueryWrapper<ANALYSIS_DASHBOARD_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ANALYSIS_DASHBOARD_INF::getF_MENU, analysisDashboardInfVo.getF_MENU());
        List<ANALYSIS_DASHBOARD_INF> analysisDashboardInfs = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(analysisDashboardInfs)) {
            throw new BusinessException(DataManagementExceptionEnum.MENU_ALREADY_ASSOCIATED_ANALYSIS_TEMPLATE);
        }
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = new ANALYSIS_DASHBOARD_INF();
        BeanUtils.copyPropertiesIgnoreNull(analysisDashboardInfVo, analysisDashboardInf);
        baseMapper.insert(analysisDashboardInf);
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = analysisDashboardTemplateInfService.getById(analysisDashboardInf.getF_ADTI());
        Integer pageType = analysisDashboardTemplateInf.getF_TYPE() == 0 ? 1 : 2;
        remoteMenuService.bindAnalysisTemplate(analysisDashboardInfVo.getF_MENU(), pageType);
    }

    @Override
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public void checkParam(ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {

    }

    @Override
    public ANALYSIS_DASHBOARD_INF findByMenuId(Long menuId) {
        LambdaQueryWrapper<ANALYSIS_DASHBOARD_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ANALYSIS_DASHBOARD_INF::getF_MENU, menuId);
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = baseMapper.selectOne(queryWrapper);
        if (analysisDashboardInf == null)
            throw new BusinessException(DataManagementExceptionEnum.ANALYSIS_DASHBOARD_NOT_EXISTS);
        return analysisDashboardInf;
    }

    @Override
    public void edit(ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo) {
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = new ANALYSIS_DASHBOARD_INF();
        BeanUtils.copyPropertiesIgnoreNull(analysisDashboardInfVo, analysisDashboardInf);
        baseMapper.updateById(analysisDashboardInf);
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = analysisDashboardTemplateInfService.getById(analysisDashboardInf.getF_ADTI());
        Integer pageType = analysisDashboardTemplateInf.getF_TYPE() == 0 ? 1 : 2;
        remoteMenuService.bindAnalysisTemplate(analysisDashboardInfVo.getF_MENU(), pageType);
    }
}





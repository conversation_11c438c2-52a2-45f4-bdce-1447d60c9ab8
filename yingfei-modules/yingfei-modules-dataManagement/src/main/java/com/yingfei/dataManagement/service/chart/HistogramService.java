package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.HistogramDTO;

import java.util.List;

public interface HistogramService {
    HistogramDTO getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    List<HistogramDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    /**
     * 获取自定义查询条件
     *
     * @param subgroupDataSelectionDTO
     * @param type                     0:异常数据和查看数据 1:控制图和直方图
     * @return
     */
    SubgroupDataSelectionDTO getDataSelection(SubgroupDataSelectionDTO subgroupDataSelectionDTO, Integer type, AnalysisChartConfigDTO analysisChartConfigDTO);

}

package com.yingfei.dataManagement.service.chart.impl;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.dataManagement.mapper.STREAM_TREND_INFMapper;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.AlarmTrendService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.HistogramService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
@Slf4j
@Service
public class AlarmTrendServiceImpl implements AlarmTrendService {

    @Resource
    private STREAM_TREND_INFMapper streamTrendInfMapper;
    @Resource
    private HistogramService histogramService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    public List<STREAM_TREND_INF_DTO> getAlarmTrend(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.ALARM_TREND);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null) return new ArrayList<>();
        if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_PART()) ||
                StringUtils.isEmpty(subgroupDataSelectionDTO.getF_TEST()) ||
                StringUtils.isEmpty(subgroupDataSelectionDTO.getF_PRCS()))
            return new ArrayList<>();

        return getStreamTrendInfDtos(subgroupDataSelectionDTO,0);
    }

    private List<STREAM_TREND_INF_DTO> getStreamTrendInfDtos(SubgroupDataSelectionDTO subgroupDataSelectionDTO,Integer type) {
        MPJLambdaWrapper<STREAM_TREND_INF> mpjBaseMapper = new MPJLambdaWrapper<>();
        mpjBaseMapper.leftJoin(PART_INF.class, PART_INF::getF_PART, STREAM_TREND_INF::getF_PART);
        mpjBaseMapper.leftJoin(PART_REV.class, PART_REV::getF_PTRV, STREAM_TREND_INF::getF_PTRV);
        mpjBaseMapper.leftJoin(PRCS_INF.class, PRCS_INF::getF_PRCS, STREAM_TREND_INF::getF_PRCS);
        mpjBaseMapper.leftJoin(TEST_INF.class, TEST_INF::getF_TEST, STREAM_TREND_INF::getF_TEST);
        mpjBaseMapper.selectAll(STREAM_TREND_INF.class)
                .selectAs(PART_INF::getF_NAME, STREAM_TREND_INF_DTO::getPartName)
                .selectAs(PART_REV::getF_NAME, STREAM_TREND_INF_DTO::getPtrvName)
                .selectAs(PRCS_INF::getF_NAME, STREAM_TREND_INF_DTO::getPrcsName)
                .selectAs(TEST_INF::getF_NAME, STREAM_TREND_INF_DTO::getTestName);
        mpjBaseMapper.eq(STREAM_TREND_INF::getF_PART, subgroupDataSelectionDTO.getF_PART())
                .eq(STREAM_TREND_INF::getF_TYPE,subgroupDataSelectionDTO.getTrendType())
                .eq(STREAM_TREND_INF::getF_PRCS, subgroupDataSelectionDTO.getF_PRCS())
                .eq(STREAM_TREND_INF::getF_TEST, subgroupDataSelectionDTO.getF_TEST())
                .eq(STREAM_TREND_INF::getF_PTRV, subgroupDataSelectionDTO.getF_REV());
        if (subgroupDataSelectionDTO.getStartTime() != null) {
            mpjBaseMapper.ge(STREAM_TREND_INF::getF_START, subgroupDataSelectionDTO.getStartTime());
        }
        if (subgroupDataSelectionDTO.getEndTime() != null) {
            mpjBaseMapper.le(STREAM_TREND_INF::getF_END, subgroupDataSelectionDTO.getEndTime());
        }
        mpjBaseMapper.orderByAsc(STREAM_TREND_INF::getF_CRTM);

        return streamTrendInfMapper.selectJoinList(STREAM_TREND_INF_DTO.class, mpjBaseMapper);
    }

    @Override
    public List<List<STREAM_TREND_INF_DTO>> getAlarmTrendList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.ALARM_TREND);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                subgroupDataSelectionDTO.getPartList(),
                subgroupDataSelectionDTO.getPtrvList(),
                subgroupDataSelectionDTO.getPrcsList(),
                subgroupDataSelectionDTO.getTestList());

        List<List<STREAM_TREND_INF_DTO>> list = new ArrayList<>();

        List<Future<List<STREAM_TREND_INF_DTO>>> futures = new ArrayList<>();

        for (Map<String, Long> map : queryMapList) {
            // 进行空值检查
            if (map == null) {
                continue;
            }
            Long fpart = map.get(Constants.part);
            Long fptrv = map.get(Constants.ptrv);
            Long fprcs = map.get(Constants.prcs);
            Long ftest = map.get(Constants.test);
//            subgroupDataSelectionDTO.setF_PART(fpart).setF_REV(fptrv).setF_PRCS(fprcs).setF_TEST(ftest);


            // 创建 subgroupDataSelectionDTO 的副本
            SubgroupDataSelectionDTO copyDto = new SubgroupDataSelectionDTO();
            // 复制原对象的属性
            copyDto.setStartTime(subgroupDataSelectionDTO.getStartTime());
            copyDto.setEndTime(subgroupDataSelectionDTO.getEndTime());
            // 设置新的参数
            copyDto.setF_PART(fpart).setF_REV(fptrv).setF_PRCS(fprcs).setF_TEST(ftest);
            // 提交任务到线程池
            futures.add(threadPoolConfig.threadPoolTaskExecutor().submit(() -> getStreamTrendInfDtos(copyDto,0)));
        }
        // 获取任务结果
        for (Future<List<STREAM_TREND_INF_DTO>> future : futures) {
            try {
                List<STREAM_TREND_INF_DTO> dto = future.get();
                if (dto != null && !dto.isEmpty()) {
                    list.add(dto);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程处理出错", e);
            }
        }
        return list;
    }

}

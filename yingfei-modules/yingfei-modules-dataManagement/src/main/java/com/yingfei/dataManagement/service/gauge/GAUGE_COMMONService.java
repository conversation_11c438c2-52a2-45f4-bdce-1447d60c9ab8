package com.yingfei.dataManagement.service.gauge;

import com.yingfei.entity.domain.GAUGE_AGENT;
import com.yingfei.entity.dto.GAUGE_AGENT_DTO;
import com.yingfei.entity.dto.GAUGE_CONNECTION_DTO;
import com.yingfei.entity.vo.GAUGE_AGENT_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;

import java.util.List;

public interface GAUGE_COMMONService {
    GAUGE_AGENT saveAgentName(GAUGE_AGENT_VO gaugeAgentVo);

    GAUGE_AGENT_DTO findByHardwareId(String hardwareId);

    List<GAUGE_CONNECTION_DTO> getGaugeConfig(Long agentId);

    Double serialAnalysis(SerialDebuggingVO serialDebuggingVO);

    /**
     * 清除缓存
     * @param serialDebuggingVO
     */
    void clean(SerialDebuggingVO serialDebuggingVO);
}

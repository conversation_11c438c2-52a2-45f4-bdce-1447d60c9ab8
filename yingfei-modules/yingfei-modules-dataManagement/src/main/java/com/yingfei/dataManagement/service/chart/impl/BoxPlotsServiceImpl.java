package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.*;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.BoxPlotsService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.HistogramService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.*;
import com.yingfei.entity.enums.BoxPlotsAnalyseTypeEnum;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.enums.TEST_INF_TYPEEnum;
import com.yingfei.entity.enums.TimeEnum;
import com.yingfei.entity.util.HistogramUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class BoxPlotsServiceImpl implements BoxPlotsService {

    @Resource
    private RedisService redisService;
    @Resource
    private SHIFT_GRPMapper shiftGrpMapper;
    @Resource
    private SHIFT_DATMapper shiftDatMapper;
    @Resource
    private TEST_INFMapper testInfMapper;
    @Resource
    private PART_INFMapper partInfMapper;
    @Resource
    private JOB_DATMapper jobDatMapper;
    @Resource
    private JOB_GRPMapper jobGrpMapper;
    @Resource
    private PRCS_INFMapper prcsInfMapper;
    @Resource
    private SGRP_DSCMapper sgrpDscMapper;
    @Resource
    private DESC_DATMapper descDatMapper;
    @Resource
    private LOT_INFMapper lotInfMapper;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private EMPL_INFMapper emplInfMapper;
    @Resource
    private HistogramService histogramService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private INSPECTION_TYPE_DATService inspectionTypeDatService;
    @Resource
    private PART_REVService partRevService;

    @Override
    public List<BoxPlotsDataDTO> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.BOX_PLOTS);
        BoxPlotsConfigDTO boxPlotsConfigDTO = new BoxPlotsConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            boxPlotsConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), BoxPlotsConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            boxPlotsConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), BoxPlotsConfigDTO.class);
        }
        /*获取聚合分析子组数据*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return new ArrayList<>();

        /*箱线图,能力仪表盘只查询测试类型为变量的*/
        subgroupDataSelectionDTO.setTestType(YesOrNoEnum.YES.getType());
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        if (subgroupDataSelectionDTO.getTestType() == YesOrNoEnum.YES.getType()) {
            subgroupDataDTOList = subgroupDataDTOList.stream().filter(s -> s.getTestType() == TEST_INF_TYPEEnum.VARIABLE.getType()).collect(Collectors.toList());
        }

        return getBoxPlotsData(subgroupDataDTOList, boxPlotsConfigDTO, subgroupDataSelectionDTO,analysisChartConfigDTO);
    }

    @Override
    public List<BoxPlotsDataDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.BOX_PLOTS);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        BoxPlotsConfigDTO boxPlotsConfigDTO = new BoxPlotsConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            boxPlotsConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), BoxPlotsConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            boxPlotsConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), BoxPlotsConfigDTO.class);
        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return new ArrayList<>();


        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);

        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return new ArrayList<>();
        }
        /*多测试拆分*/
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        /*过滤对应的条件*/
        SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
        subgroupDataDTOList = subgroupDataDTOList.stream().filter(s ->
                        finalSubgroupDataSelectionDTO.getPartList().contains(s.getF_PART()) &&
                                finalSubgroupDataSelectionDTO.getPtrvList().contains(s.getF_REV()) &&
                                finalSubgroupDataSelectionDTO.getPrcsList().contains(s.getF_PRCS()) &&
                                finalSubgroupDataSelectionDTO.getTestList().contains(s.getF_TEST()))
                .collect(Collectors.toList());

        if (subgroupDataSelectionDTO.getTestType() == YesOrNoEnum.YES.getType()) {
            subgroupDataDTOList = subgroupDataDTOList.stream().filter(s -> s.getTestType() == TEST_INF_TYPEEnum.VARIABLE.getType()).collect(Collectors.toList());
        }
        return getBoxPlotsData(subgroupDataDTOList, boxPlotsConfigDTO, subgroupDataSelectionDTO,analysisChartConfigDTO);

    }


    public List<BoxPlotsDataDTO> getBoxPlotsData(List<SubgroupDataDTO> sgrpExtList, BoxPlotsConfigDTO boxPlotsConfigDTO, SubgroupDataSelectionDTO subgroupDataSelectionDTO,AnalysisChartConfigDTO analysisChartConfigDTO) {
        if (boxPlotsConfigDTO == null) {
            boxPlotsConfigDTO = BoxPlotsConfigDTO.initList();
        }

        sgrpExtList = chartCommonService.getTotalNumSubgroup(sgrpExtList, subgroupDataSelectionDTO.getMaxNum(), 2);

        Map<Integer, AnalyseTypeDTO> analyseTypeMap = boxPlotsConfigDTO.getAnalyseTypeMap();

        List<BoxPlotsDataDTO> list = new ArrayList<>();

        AnalyseTypeDTO levelOne = analyseTypeMap.get(1);

        if (levelOne == null)
            throw new BaseException("层级解析错误");

        Map<Long, List<SubgroupDataDTO>> oneMap = getList(sgrpExtList, levelOne);

        BoxPlotsConfigDTO finalBoxPlotsConfigDTO = boxPlotsConfigDTO;
        oneMap.forEach((k, v) -> {
            BoxPlotsDataDTO boxPlotsDataDTO = new BoxPlotsDataDTO();
            boxPlotsDataDTO.setLevel(1);
            /*将失效子组排除*/
            List<SubgroupDataDTO> collect = v.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) return;
            getBoxPlotsDataDTO(boxPlotsDataDTO, finalBoxPlotsConfigDTO, levelOne, k, collect);
            list.add(boxPlotsDataDTO);
        });
        list.sort(new Comparator<BoxPlotsDataDTO>() {
            @Override
            public int compare(BoxPlotsDataDTO o1, BoxPlotsDataDTO o2) {
                String s1 = o1.getName();
                String s2 = o2.getName();
                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                return Collator.getInstance(Locale.UK).compare(s1, s2);
            }
        });

        /*箱线图*/
        List<BoxPlotsParticularsDTO> childList = new ArrayList<>();
        getChildData(list, childList);

        String key = String.format(RedisConstant.BOX_PLOTS_DATA, subgroupDataSelectionDTO.getParameterId());
        redisService.deleteObject(key);
        if (CollectionUtils.isNotEmpty(childList)) {
            redisService.setCacheList(key, childList, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
        }
        return list;
    }

    @Override
    public void editConfig(Long menuId, String chartId, BoxPlotsConfigDTO boxPlotsConfigDTO, ChartTypeEnum chartTypeEnum) {
        analysisDashboardTemplateInfService.editConfig(menuId, chartId, boxPlotsConfigDTO, chartTypeEnum);
    }

    public static void getChildData(List<BoxPlotsDataDTO> list, List<BoxPlotsParticularsDTO> childList) {
        for (BoxPlotsDataDTO plotsDataDTO : list) {
            if (plotsDataDTO.getParticulars() != null) {
                childList.add(plotsDataDTO.getParticulars());
            }
            if (CollectionUtils.isNotEmpty(plotsDataDTO.getBoxPlotsDataChildList())) {
                getChildData(plotsDataDTO.getBoxPlotsDataChildList(), childList);
            }
        }
    }

    /**
     * 递归
     *
     * @param boxPlotsDataDTO
     * @param boxPlotsConfigDTO
     * @return
     */
    public void initData(BoxPlotsDataDTO boxPlotsDataDTO, BoxPlotsConfigDTO boxPlotsConfigDTO, List<SubgroupDataDTO> extList, List<BoxPlotsDataDTO> list) {
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = boxPlotsConfigDTO.getAnalyseTypeMap();
        AnalyseTypeDTO analyseTypeDTO = analyseTypeMap.get(boxPlotsDataDTO.getLevel());
        if (analyseTypeDTO == null) {
            return;
        }

        Map<Long, List<SubgroupDataDTO>> map = getList(extList, analyseTypeDTO);
        map.forEach((k, v) -> {
            BoxPlotsDataDTO plotsDataDTO = new BoxPlotsDataDTO();
            plotsDataDTO.setLevel(boxPlotsDataDTO.getLevel());
            /*将失效子组排除*/
            List<SubgroupDataDTO> collect = v.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) return;
            BoxPlotsDataDTO dataDTO = getBoxPlotsDataDTO(plotsDataDTO, boxPlotsConfigDTO, analyseTypeDTO, k, collect);
            list.add(dataDTO);
        });


    }

    private BoxPlotsDataDTO getBoxPlotsDataDTO(BoxPlotsDataDTO boxPlotsDataDTO, BoxPlotsConfigDTO boxPlotsConfigDTO,
                                               AnalyseTypeDTO level, Long k, List<SubgroupDataDTO> v) {
        BoxPlotsDataDTO dataDTO = new BoxPlotsDataDTO();
        dataDTO.setName("不存在");
        dataDTO.setLevel(boxPlotsDataDTO.getLevel() + 1);
        Map<Integer, AnalyseTypeDTO> analyseTypeMap = boxPlotsConfigDTO.getAnalyseTypeMap();
        AnalyseTypeDTO analyseTypeDTO = analyseTypeMap.get(boxPlotsDataDTO.getLevel());
        /*计算箱线图点*/
//        if (boxPlotsConfigDTO.getType() == 1) {
//            v = v.stream().filter(s -> s.getSgrpValDto().getF_SBSZ() != null && s.getSgrpValDto().getF_SBSZ() > 0).collect(Collectors.toList());
//        } else if (boxPlotsConfigDTO.getType() == 0) {
//            v = v.stream().filter(s -> !ObjectUtils.isEmpty(s.getSgrpValDto()) && s.getSgrpValDto().getF_SBSZ() != null && s.getSgrpValDto().getF_SBSZ() == 0).collect(Collectors.toList());
//        }
        boxPlotsDataDTO.setName(getName(analyseTypeDTO, k));
        BoxPlotsParticularsDTO boxPlotsParticularsDTO = new BoxPlotsParticularsDTO();
        List<Double> doubles = getBoxValues(boxPlotsConfigDTO, v, boxPlotsParticularsDTO);
        boxPlotsParticularsDTO.setName(boxPlotsDataDTO.getName());

        if (CollectionUtils.isEmpty(doubles)) return boxPlotsDataDTO;
        /*按从小到大排序*/
        doubles.sort(Comparator.comparing(Double::doubleValue));
        int q75 = (int) Math.ceil(doubles.size() * 0.75d);
        Double q75Val = doubles.get(q75 < 5 ? q75 - 1 : q75);
        int q25 = (int) Math.ceil(doubles.size() * 0.25d);
        Double q25Val = doubles.get(q25 < 4 ? q25 - 1 : q25);
        int q50 = (int) Math.ceil(doubles.size() * 0.5d);
        Double q50Val = doubles.get(q50 < 5 ? q50 - 1 : q50);
        double iqr = BigDecimal.valueOf(q75Val).subtract(BigDecimal.valueOf(q25Val)).doubleValue();
        BigDecimal multiply = BigDecimal.valueOf(1.5d).multiply(BigDecimal.valueOf(iqr));
        double max = BigDecimal.valueOf(q75Val).add(multiply).doubleValue();
        double min = BigDecimal.valueOf(q25Val).subtract(multiply).doubleValue();
        List<Double> dataList = new ArrayList<>();
        dataList.add(min);
        dataList.add(q25Val);
        dataList.add(q50Val);
        dataList.add(q75Val);
        dataList.add(max);
        boxPlotsParticularsDTO.setQ1(q25Val);
        boxPlotsParticularsDTO.setQ2(q50Val);
        boxPlotsParticularsDTO.setQ3(q75Val);
        boxPlotsParticularsDTO.setQMax(max);
        boxPlotsParticularsDTO.setQMin(min);
        boxPlotsDataDTO.setParticulars(boxPlotsParticularsDTO);
        List<BoxPlotsDataDTO> list = new ArrayList<>();
        initData(dataDTO, boxPlotsConfigDTO, v, list);
        list.sort(new Comparator<BoxPlotsDataDTO>() {
            @Override
            public int compare(BoxPlotsDataDTO o1, BoxPlotsDataDTO o2) {
                String s1 = o1.getName();
                String s2 = o2.getName();
                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
                return Collator.getInstance(Locale.UK).compare(s1, s2);
            }
        });
        boxPlotsDataDTO.setData(dataList);
        boxPlotsDataDTO.setBoxPlotsDataChildList(list);
        return boxPlotsDataDTO;
    }

    private List<Double> getBoxValues(BoxPlotsConfigDTO boxPlotsConfigDTO, List<SubgroupDataDTO> extList, BoxPlotsParticularsDTO boxPlotsParticularsDTO) {
        List<Double> boxValues = new ArrayList<>();
        /*将pptv作为key生成map*/
        Map<String, List<SubgroupDataDTO>> map = extList.stream()
                .collect(Collectors.groupingBy(s -> s.getF_PART() + Constants.COMMA
                                + s.getF_REV()+ Constants.COMMA
                                + s.getF_TEST()+Constants.COMMA
                                +s.getF_PRCS(),
                        LinkedHashMap::new, Collectors.toList()));
        HashMap<String, SPEC_INF_DTO> specLimMap = new HashMap<>();
        /*计算每个公差限的统计值*/
        map.forEach((k, v) -> {
            DataSummaryDTO dataSummaryDTO = new DataSummaryDTO();
            /*获取公差限数据  目标cp和目标cpk*/
            SPEC_INF_DTO specInfDto = specInfService.getSpecLim(dataSummaryDTO, v.get(0));
            if (specInfDto != null) {
                specInfDto.setDataSummaryDTO(dataSummaryDTO);
                specLimMap.put(k, specInfDto);
            }
        });
        /*判断所有公差的值是否一样*/
        boolean allSpecEqual = !specLimMap.isEmpty()
                && specLimMap.values().stream().map(SPEC_INF_DTO::getF_USL).distinct().count() == 1
                && specLimMap.values().stream().map(SPEC_INF_DTO::getF_LSL).distinct().count() == 1
                && specLimMap.values().stream().allMatch(dto ->
                dto.getF_USL() != null
                        || dto.getF_LSL() != null);
        /*如果分组数=1
         * 存在公差
         * 1. 如果需要进行标准化，按分组处理每个数值，最后使用（-1，1）来计算所有统计值
         * 2. 如果不需要标准化，使用实际公差计算所有统计值
         * 不存在公差
         * 1. 计算部分统计值
         * 如果分组数>1，分组数与公差限数量一致，且每个分组都的公差限都有上下公差
         * 1. 如果需要进行标准化，按分组处理每个数值，最后使用（-1，1）来计算所有统计值
         * 2. 如果不需要标准化，值计算部分统计值
         * 如果分组数>1，分组数与公差限数量一致，且每个分组都的公差限（USL LSL）都一样
         * 1. 使用实际公差计算所有统计值
         * 其他情况（specLimMap为空，specLimMap数量小于分组数[部分组合没有公差]，specLimMap中部分界限没有双边公差
         * 1. 计算部分统计值
         * */
        int senario = 0;
        if((map.size()==1 && specLimMap.size()==1 &&
                boxPlotsConfigDTO.getIsStandardized()==0 &&
                specLimMap.values().stream().allMatch(s -> s.getF_USL() != null && s.getF_LSL() != null))
                ||
                (map.size() == specLimMap.size() && specLimMap.values().stream().allMatch(s -> s.getF_USL() != null && s.getF_LSL() != null)
                        && boxPlotsConfigDTO.getIsStandardized() ==0)
        ){
            /*情景1：分组=1，双边公差，需要标准化；如果分组数>1，每个分组都的公差限都有上下公差，且需要标准化 */
            /*分组处理：（实际值-目标值）/ 容差*/
            for (String k : map.keySet()) {
                List<SubgroupDataDTO> subgroupDataDTOS = map.get(k);
                SPEC_INF_DTO specLim = specLimMap.get(k);
                List<Double> rawValues = getMeasureValues(subgroupDataDTOS,boxPlotsConfigDTO);
                for (Double rawValue : rawValues) {
                    double tarValue = 0.5d * (specLim.getF_LSL() + specLim.getF_USL());
                    double standardizedValue = (rawValue - tarValue) / (specLim.getF_USL() - specLim.getF_LSL());
                    boxValues.add(standardizedValue);
                }
            }
            /*使用（-1，1）计算所有统计值*/
            boxPlotsParticularsDTO.setLsl(-1d).setTar(0d).setUsl(1d);
        }
        else if((map.size()==1 && specLimMap.size()==1   &&
                ( (specLimMap.values().stream().collect(Collectors.toList()).get(0).getF_USL() == null ||
                        specLimMap.values().stream().collect(Collectors.toList()).get(0).getF_LSL() == null)
                        || ((specLimMap.values().stream().collect(Collectors.toList()).get(0).getF_USL() != null &&
                        specLimMap.values().stream().collect(Collectors.toList()).get(0).getF_LSL() != null) && boxPlotsConfigDTO.getIsStandardized()==1)))
                ||(map.size()>1 && specLimMap.size()==map.size() && allSpecEqual))
        {
            /*情景2：分组=1，单边公差或者双边不需要标准化；分组>1，不是双边公差，但公差都一样 */
            boxValues = getMeasureValues(extList,boxPlotsConfigDTO);
            senario = 1;
            /*使用实际公差限计算统计值*/
            var specLim = specLimMap.values().stream().collect(Collectors.toList()).get(0);
            boxPlotsParticularsDTO.setLsl(specLim.getF_LSL());
            boxPlotsParticularsDTO.setUsl(specLim.getF_USL());
            boxPlotsParticularsDTO.setTar(specLim.getF_TAR());
        }
        else
        {
            boxValues = getMeasureValues(extList,boxPlotsConfigDTO);
            senario=2;
        }
        if(senario<2) {
            /*计算所有统计值*/
            /*计算标准差*/
            boxPlotsParticularsDTO.setSd(HistogramUtil.getLongTermStandardDeviation(boxValues))
                    .setMean(boxValues.stream().mapToDouble(Double::doubleValue).average().orElse(0d))
                    .setMin(boxValues.stream().min(Double::compareTo).orElse(0d))
                    .setMax(boxValues.stream().max(Double::compareTo).orElse(0d))
                    .setNum(boxValues.size());
            boxPlotsParticularsDTO.setPp(HistogramUtil.getPp(boxPlotsParticularsDTO.getUsl(),
                    boxPlotsParticularsDTO.getLsl(), boxPlotsParticularsDTO.getMean(), boxPlotsParticularsDTO.getSd()));
            boxPlotsParticularsDTO.setPpk(HistogramUtil.getPpk(boxPlotsParticularsDTO.getUsl(),
                    boxPlotsParticularsDTO.getLsl(), boxPlotsParticularsDTO.getMean(), boxPlotsParticularsDTO.getSd()));
            List<Double> list = boxValues.stream().filter(d -> (boxPlotsParticularsDTO.getUsl() != null && d > boxPlotsParticularsDTO.getUsl())
                    || (boxPlotsParticularsDTO.getLsl() != null && d < boxPlotsParticularsDTO.getLsl())).collect(Collectors.toList());
            boxPlotsParticularsDTO.setOos(list.size()).setOosRatio(list.size() * 1d / boxValues.size());
        }
        else
        {
            /*计算部分统计值*/
            boxPlotsParticularsDTO.setMin(boxValues.stream().min(Double::compareTo).orElse(0d));
            boxPlotsParticularsDTO.setMax(boxValues.stream().max(Double::compareTo).orElse(0d));
            boxPlotsParticularsDTO.setNum(boxValues.size());

        }
        return boxValues;
    }

    private List<Double> getMeasureValues(List<SubgroupDataDTO> subgroups, BoxPlotsConfigDTO boxPlotsConfigDTO) {
        List<Double> measureValues = new ArrayList<>();
        if(boxPlotsConfigDTO.getType() == 0){
            /*取单件值*/
            return subgroups.stream().map(SubgroupDataDTO::getSgrpValDto)
                    .map(SGRP_VAL_DTO::getSgrpValChildDto)
                    .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList());
        }
        else
        {
            /*取件内值*/
            final List<SGRP_VAL_DTO> collect = subgroups.stream().map(SubgroupDataDTO::getSgrpValDto).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)) {
                final List<SGRP_VAL_CHILD_DTO> collect1 = collect.stream().map(SGRP_VAL_DTO::getSgrpValChildDto).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect1)) {
                    final List<SGRP_VAL_CHILD_DTO.Test> collect2 = collect1.stream().map(SGRP_VAL_CHILD_DTO::getTestList).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect2)) {
                        final List<SGRP_VAL_CHILD_DTO.SubTest> collect3 = collect2.stream()
                                .map(SGRP_VAL_CHILD_DTO.Test::getSubTestList)
                                .filter(Objects::nonNull) // 过滤掉 null 的子列表
                                .flatMap(List::stream)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect3)) {
                            measureValues = collect3.stream().map(SGRP_VAL_CHILD_DTO.SubTest::getSubTestValue).collect(Collectors.toList());
                        }
                    }
                }
            }
        }
        return measureValues;
    }



    public Map<Long, List<SubgroupDataDTO>> getList(List<SubgroupDataDTO> subgroupDataDTOList, AnalyseTypeDTO analyseTypeDTO) {
        Map<Long, List<SubgroupDataDTO>> map = new HashMap<>();
        switch (BoxPlotsAnalyseTypeEnum.getByCode(analyseTypeDTO.getAnalyseType())) {
            case SHIFT_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_SHIFT() != null && s.getF_SHIFT() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_SHIFT,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case SHIFT_GRP:
                /*获取对应班次*/
                map = subgroupDataDTOList.stream().filter(s -> s.getF_SHIFT() != null && s.getF_SHIFT() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_SHIFT,
                                LinkedHashMap::new, Collectors.toList()));
                List<SHIFT_DAT> shiftDats;
                if (MapUtils.isEmpty(map)) {
                    shiftDats = new ArrayList<>();
                } else {
                    LambdaQueryWrapper<SHIFT_DAT> shift = new LambdaQueryWrapper<>();
                    shift.in(SHIFT_DAT::getF_SHIFT, map.keySet());
                    shiftDats = shiftDatMapper.selectList(shift);
                }
                Map<Long, List<SubgroupDataDTO>> shiftGrpMap = new HashMap<>();
                Map<Long, List<SubgroupDataDTO>> shiftGrpExtList = map;
                shiftDats.forEach(shftDat -> {
                    if (shiftGrpMap.get(shftDat.getF_SHGP()) == null) {
                        shiftGrpMap.put(shftDat.getF_SHGP(), shiftGrpExtList.get(shftDat.getF_SHIFT()));
                    } else {
                        shiftGrpMap.get(shftDat.getF_SHGP()).addAll(shiftGrpExtList.get(shftDat.getF_SHIFT()));
                    }
                });

                map = shiftGrpMap;
                break;
            case TEST_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_TEST() != null && s.getF_TEST() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_TEST,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case TEST_NO:
//                map = subgroupDataDTOList.stream().filter(s -> s.getSgrpValDto().getSgrpValChildDto().getF_TSNO() != null)
//                        .collect(Collectors.groupingBy(s -> s.getF_TSNO().toString(),
//                                LinkedHashMap::new, Collectors.toList()));
                break;
            case PART_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_PART() != null && s.getF_PART() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_PART,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case JOB_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_JOB() != null && s.getF_JOB() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_JOB,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case JOB_GRP:
                /*获取对应工作*/
                map = subgroupDataDTOList.stream().filter(s -> s.getF_JOB() != null && s.getF_JOB() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_JOB,
                                LinkedHashMap::new, Collectors.toList()));
                List<JOB_DAT> jobDats;
                if (MapUtils.isEmpty(map)) {
                    jobDats = new ArrayList<>();
                } else {
                    LambdaQueryWrapper<JOB_DAT> jobDatLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    jobDatLambdaQueryWrapper.in(JOB_DAT::getF_JOB, map.keySet());
                    jobDats = jobDatMapper.selectList(jobDatLambdaQueryWrapper);
                }
                Map<Long, List<SubgroupDataDTO>> jobGrpMap = new HashMap<>();
                Map<Long, List<SubgroupDataDTO>> jobGrpExtList = map;
                jobDats.forEach(jobDat -> {
                    if (jobGrpMap.get(jobDat.getF_JBGP()) == null) {
                        jobGrpMap.put(jobDat.getF_JBGP(), jobGrpExtList.get(jobDat.getF_JOB()));
                    } else {
                        jobGrpMap.get(jobDat.getF_JBGP()).addAll(jobGrpExtList.get(jobDat.getF_JOB()));
                    }
                });

                map = jobGrpMap;
                break;
            case PRCS_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_PRCS() != null && s.getF_PRCS() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_PRCS,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case DESC_DAT:
                /*获取描述符组对应子组*/
                LambdaQueryWrapper<SGRP_DSC> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SGRP_DSC::getF_DSGP, analyseTypeDTO.getGroupId());
                List<SGRP_DSC> sgrpDscs = sgrpDscMapper.selectList(queryWrapper);
                Map<Long, List<SGRP_DSC>> sgrpMap = sgrpDscs.stream().collect(Collectors.groupingBy(SGRP_DSC::getF_DESC,
                        LinkedHashMap::new, Collectors.toList()));
                Map<Long, List<SubgroupDataDTO>> descMap = new HashMap<>();
                sgrpMap.forEach((k, v) -> {
                    List<Long> list = v.stream().map(SGRP_DSC::getF_SGRP).collect(Collectors.toList());
                    List<SubgroupDataDTO> exts = subgroupDataDTOList.stream().filter(s -> list.contains(s.getF_SGRP())).collect(Collectors.toList());
                    descMap.put(k, exts);
                });
                map = descMap;
                break;
            case LOT_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_LOT() != null && s.getF_LOT() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_LOT,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case TIME:
                /*获取最大和最小子组时间*/
                int max = (int) (subgroupDataDTOList.stream().mapToLong(s -> s.getF_SGTM().getTime()).max().orElse(0L) / 1000);
                int min = (int) (subgroupDataDTOList.stream().mapToLong(s -> s.getF_SGTM().getTime()).min().orElse(0L) / 1000);
                if (max == 0L || min == 0L) break;
                switch (TimeEnum.getType(analyseTypeDTO.getTimeType())) {
                    case SECOND:
                        for (long i = 0; ; i += analyseTypeDTO.getTimeInterval()) {
                            long start = min + i * analyseTypeDTO.getTimeInterval();
                            long end = min + (i + 1) * analyseTypeDTO.getTimeInterval();
                            if (start > (long) max) break;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    case MINUTE:
                        int maxMinute = (max + analyseTypeDTO.getTimeInterval() * 60) / 60;
                        int minMinute = (min - analyseTypeDTO.getTimeInterval() * 60) / 60;
                        for (int i = minMinute; i < maxMinute; i += analyseTypeDTO.getTimeInterval()) {
                            long start = i * 60L;
                            long end = (i + analyseTypeDTO.getTimeInterval()) * 60L;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    case HOUR:
                        int maxHour = (max + analyseTypeDTO.getTimeInterval() * 3600) / 3600;
                        int minHour = (min - analyseTypeDTO.getTimeInterval() * 3600) / 3600;
                        for (int i = minHour; i < maxHour; i += analyseTypeDTO.getTimeInterval()) {
                            long start = i * 3600L;
                            long end = (i + analyseTypeDTO.getTimeInterval()) * 3600L;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    case DAY:
                        int maxDay = (max + analyseTypeDTO.getTimeInterval() * 86400) / 86400;
                        int minDay = (min - analyseTypeDTO.getTimeInterval() * 86400) / 86400;
                        for (int i = minDay; i < maxDay; i += analyseTypeDTO.getTimeInterval()) {
                            long start = i * 86400L;
                            long end = (i + analyseTypeDTO.getTimeInterval()) * 86400L;
                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    case WEEK:
                        Date weekDate = new Date(min * 1000L);
                        Calendar weekCalendar = Calendar.getInstance();
                        weekCalendar.setTime(weekDate);
                        weekCalendar.set(Calendar.HOUR_OF_DAY, 0);
                        weekCalendar.set(Calendar.MINUTE, 0);
                        weekCalendar.set(Calendar.SECOND, 0);
                        // 设置到周一
                        weekCalendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                        for (int i = 0; ; i++) {
                            long start = weekCalendar.getTimeInMillis() / 1000L;
                            if (start > max) break;
                            /*加一周*/
                            weekCalendar.add(Calendar.WEEK_OF_YEAR, analyseTypeDTO.getTimeInterval());
                            Date nextTime = weekCalendar.getTime();
                            weekCalendar.setTime(nextTime);
                            long end = nextTime.getTime() / 1000L;

                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    case MONTH:
                        Date monthDate = new Date(min * 1000L);
                        Calendar monthCalendar = Calendar.getInstance();
                        monthCalendar.setTime(monthDate);
                        monthCalendar.set(Calendar.HOUR_OF_DAY, 0);
                        monthCalendar.set(Calendar.MINUTE, 0);
                        monthCalendar.set(Calendar.SECOND, 0);
                        /*设置日为1*/
                        monthCalendar.set(Calendar.DAY_OF_MONTH, 1);
                        for (int i = 0; ; i++) {
                            long start = monthCalendar.getTimeInMillis() / 1000L;
                            if (start > max) break;
                            /*加一个月*/
                            monthCalendar.add(Calendar.MONTH, analyseTypeDTO.getTimeInterval());
                            Date nextTime = monthCalendar.getTime();
                            monthCalendar.setTime(nextTime);
                            long end = nextTime.getTime() / 1000L;

                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    case QUARTER:
                        Date quarterDate = new Date(min * 1000L);
                        Calendar quarterCalendar = Calendar.getInstance();
                        quarterCalendar.setTime(quarterDate);
                        quarterCalendar.set(Calendar.HOUR_OF_DAY, 0);
                        quarterCalendar.set(Calendar.MINUTE, 0);
                        quarterCalendar.set(Calendar.SECOND, 0);
                        // 获取当前月份（月份从0开始，所以需要加1）
                        int month = quarterCalendar.get(Calendar.MONTH) + 1;
                        /*获取季度开始月份*/
                        double ceil = Math.ceil(month / 3D);
                        int quarterStartMonth = (int) (ceil * 3) - 2;
                        quarterCalendar.set(Calendar.MONTH, quarterStartMonth);
                        quarterCalendar.set(Calendar.DAY_OF_MONTH, 1);
                        for (int i = 0; ; i++) {
                            long start = quarterCalendar.getTimeInMillis() / 1000L;
                            if (start > max) break;
                            /*加一个季度*/
                            quarterCalendar.add(Calendar.MONTH, 3 * analyseTypeDTO.getTimeInterval());
                            Date nextTime = quarterCalendar.getTime();
                            quarterCalendar.setTime(nextTime);
                            long end = nextTime.getTime() / 1000L;

                            List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                                    .filter(s -> s.getF_SGTM().getTime() / 1000 > start && s.getF_SGTM().getTime() / 1000 < end)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect))
                                map.put(start, collect);
                        }
                        break;
                    default:
                }
                break;
            case STAFF:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_CRUE() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_CRUE,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case INSPECTION_TYPE:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_INSPECTION_TYPE() != null && s.getF_INSPECTION_TYPE() != 0L)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_INSPECTION_TYPE,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            case PTRV_DAT:
                map = subgroupDataDTOList.stream().filter(s -> s.getF_REV() != null)
                        .collect(Collectors.groupingBy(SubgroupDataDTO::getF_REV,
                                LinkedHashMap::new, Collectors.toList()));
                break;
            default:
        }
        return map;
    }


    public String getName(AnalyseTypeDTO analyseTypeDTO, Long k) {
        String name = "不存在";
        switch (BoxPlotsAnalyseTypeEnum.getByCode(analyseTypeDTO.getAnalyseType())) {
            case SHIFT_DAT:
                SHIFT_DAT shftDat = shiftDatMapper.selectById(k);
                if (shftDat != null) name = shftDat.getF_NAME();
                break;
            case SHIFT_GRP:
                SHIFT_GRP shftGrp = shiftGrpMapper.selectById(k);
                if (shftGrp != null) name = shftGrp.getF_NAME();
                break;
            case TEST_DAT:
                TEST_INF testDat = testInfMapper.selectById(k);
                if (testDat != null) name = testDat.getF_NAME();
                break;
            case TEST_NO:
                name = "测试编号:" + k;
                break;
            case PART_DAT:
                PART_INF partDat = partInfMapper.selectById(k);
                if (partDat != null) name = partDat.getF_NAME();
                break;
            case JOB_DAT:
                JOB_DAT jobDat = jobDatMapper.selectById(k);
                if (jobDat != null) name = jobDat.getF_NAME();
                break;
            case JOB_GRP:
                JOB_GRP jobGrp = jobGrpMapper.selectById(k);
                if (jobGrp != null) name = jobGrp.getF_NAME();
                break;
            case PRCS_DAT:
                PRCS_INF prcsDat = prcsInfMapper.selectById(k);
                if (prcsDat != null) name = prcsDat.getF_NAME();
                break;
            case DESC_DAT:
                DESC_DAT descDat = descDatMapper.selectById(k);
                if (descDat != null) name = descDat.getF_NAME();
                break;
            case LOT_DAT:
                LOT_INF partLot = lotInfMapper.selectById(k);
                if (partLot != null) name = partLot.getF_NAME();
                break;
            case TIME:
                switch (TimeEnum.getType(analyseTypeDTO.getTimeType())) {
                    case SECOND:
                        Date secondDate = new Date(k * 1000L);
                        name = DateUtils.dateTimeTwo(secondDate);
                        break;
                    case MINUTE:
                        Date minuteDate = new Date(k * 1000L);
                        name = DateUtils.dateTimeFour(minuteDate);
                        break;
                    case HOUR:
                        Date hourDate = new Date(k * 1000L);
                        name = DateUtils.dateTimeFive(hourDate);
                        break;
                    case DAY:
                    case WEEK:
                        Date dayDate = new Date(k * 1000L);
                        name = DateUtils.dateTime(dayDate);
                        break;
                    case MONTH:
                    case QUARTER:
                        Date monthDate = new Date(k * 1000L);
                        name = DateUtils.dateTimeThree(monthDate);
                        break;
                    default:
                }
                break;
            case STAFF:
                EMPL_INF emplInf = emplInfMapper.selectById(k);
                if (emplInf != null) name = emplInf.getF_NAME();
                break;
            case INSPECTION_TYPE:
                INSPECTION_TYPE_DAT inspectionTypeDat = inspectionTypeDatService.getById(k);
                if (inspectionTypeDat != null) name = inspectionTypeDat.getF_NAME();
                break;
            case PTRV_DAT:
                PART_REV partRev = partRevService.getById(k);
                if (partRev != null) name = partRev.getF_NAME();
                break;
            default:
        }
        return name;
    }


    public static void main(String[] args) {
//        Date date = new Date();
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.SECOND, 0);
//        // 设置到周一
////        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//        /*设置日为1*/
////        calendar.set(Calendar.DAY_OF_MONTH, 1);
//        calendar.add(Calendar.MONTH, 15);
//        System.out.println(calendar.getTimeInMillis());
//        List<BoxPlotsDataDTO> list = new ArrayList<>();
//        BoxPlotsDataDTO plotsDataDTO = new BoxPlotsDataDTO();
//        plotsDataDTO.setName("Od-Loc A");
//        list.add(plotsDataDTO);
//        BoxPlotsDataDTO plotsDataDTO2 = new BoxPlotsDataDTO();
//        plotsDataDTO2.setName("Od-Loc C");
//        list.add(plotsDataDTO2);
//        BoxPlotsDataDTO plotsDataDTO3 = new BoxPlotsDataDTO();
//        plotsDataDTO3.setName("Od-Loc B");
//        list.add(plotsDataDTO3);
//
//        list.sort(new Comparator<BoxPlotsDataDTO>() {
//            public int compare(BoxPlotsDataDTO o1, BoxPlotsDataDTO o2) {
//                String s1 = o1.getName();
//                String s2 = o2.getName();
//                //该排序为正序排序，如果倒序排序则将compare中的s2和s1互换位置
//                return Collator.getInstance(Locale.UK).compare(s1, s2);
//            }
//        });
//        System.err.println();

        int q75 = (int) Math.ceil(5 * 0.75d);
        System.err.println();
    }
}

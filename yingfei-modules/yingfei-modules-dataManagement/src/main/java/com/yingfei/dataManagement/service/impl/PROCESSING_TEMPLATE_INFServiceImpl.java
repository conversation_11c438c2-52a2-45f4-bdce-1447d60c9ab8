package com.yingfei.dataManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.PROCESSING_TEMPLATE_INFMapper;
import com.yingfei.dataManagement.service.PROCESSING_TEMPLATE_INFService;
import com.yingfei.entity.domain.PRCS_INF;
import com.yingfei.entity.domain.PROCESSING_TEMPLATE_INF;
import com.yingfei.entity.dto.PROCESSING_TEMPLATE_INF_DTO;
import com.yingfei.entity.vo.PROCESSING_TEMPLATE_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【PROCESSING_TEMPLATE_INF(储存数据标准化信息表)】的数据库操作Service实现
* @createDate 2024-05-08 16:28:14
*/
@Service
public class PROCESSING_TEMPLATE_INFServiceImpl extends ServiceImpl<PROCESSING_TEMPLATE_INFMapper, PROCESSING_TEMPLATE_INF>
    implements PROCESSING_TEMPLATE_INFService {

    @Override
    public long getTotal(PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        return baseMapper.getTotal(processingTemplateInfVo);
    }

    @Override
    public List<PROCESSING_TEMPLATE_INF_DTO> getList(PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        return baseMapper.getList(processingTemplateInfVo);
    }

    @Override
    public void add(PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        PROCESSING_TEMPLATE_INF processingTemplateInf = new PROCESSING_TEMPLATE_INF();
        BeanUtils.copyPropertiesIgnoreNull(processingTemplateInfVo, processingTemplateInf);
        baseMapper.insert(processingTemplateInf);
    }

    @Override
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<PROCESSING_TEMPLATE_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(PROCESSING_TEMPLATE_INF::getF_PSTP, ids)
                .set(PROCESSING_TEMPLATE_INF::getF_DEL, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void checkParam(PROCESSING_TEMPLATE_INF_VO processingTemplateInfVo) {
        LambdaQueryWrapper<PROCESSING_TEMPLATE_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PROCESSING_TEMPLATE_INF::getF_NAME, processingTemplateInfVo.getF_NAME()).eq(PROCESSING_TEMPLATE_INF::getF_DEL, DelFlagEnum.USE.getType());
        List<PROCESSING_TEMPLATE_INF> processingTemplateInfList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(processingTemplateInfList)) return;
        if (StringUtils.isNotEmpty(processingTemplateInfVo.getF_PSTP())) {
            if (processingTemplateInfList.size() > 1 || !Objects.equals(processingTemplateInfList.get(0).getF_PSTP(), processingTemplateInfVo.getF_PSTP())) {
                throw new BusinessException(DataManagementExceptionEnum.PROCESSING_TEMPLATE_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (processingTemplateInfList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.PROCESSING_TEMPLATE_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }
}





package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.HistogramService;
import com.yingfei.dataManagement.service.manufacturing.MANUFACTURING_NODE_INFService;
import com.yingfei.entity.domain.SGRP_VAL;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.HistogramConfigDTO;
import com.yingfei.entity.dto.chart.HistogramDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.enums.STANDARDIZE_TYPEEnum;
import com.yingfei.entity.enums.TEST_INF_TYPEEnum;
import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.util.JohnSonConversionBaseUtil;
import com.yingfei.entity.util.TransformedResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HistogramServiceImpl implements HistogramService {

    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private SGRP_VALService sgrpValService;
    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;
    @Resource
    private MANUFACTURING_NODE_INFService manufacturingNodeInfService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private DEF_DATService defDatService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private RedisService redisService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Value("${pythonPath.johnson}")
    private String johnsonPath;
    @Value("${pythonPath.histogram}")
    private String histogramPath;

    @Override
    public HistogramDTO getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.HISTOGRAM);

        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
        HistogramConfigDTO histogramConfigDTO;
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            histogramConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), HistogramConfigDTO.class);
        } else {
            histogramConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), HistogramConfigDTO.class);
        }

        HistogramDTO histogramDTO = new HistogramDTO();

        /*获取缓存查询条件*/
        Integer isTopOne = analysisChartConfigDTO.getIsTopOne();
        if (isTopOne.equals(YesOrNoEnum.YES.getType())) {
            analysisChartConfigDTO.setIsTopOne(YesOrNoEnum.NO.getType());
        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return histogramDTO;

        analysisChartConfigDTO.setIsTopOne(isTopOne);

        /*获取测试是否为变量类型*/
        TEST_INF testInf = testInfService.getById(subgroupDataSelectionDTO.getF_TEST());
        if (testInf == null || !testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.VARIABLE.getType())) return histogramDTO;

        /*获取缓存子组*/
        List<SubgroupDataDTO> scrpExtList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);

        /*将失效子组排除*/
        scrpExtList = scrpExtList.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scrpExtList)) {
            return histogramDTO;
        }

        return getHistogramDTO(subgroupDataSelectionDTO, scrpExtList, analysisChartConfigDTO, histogramConfigDTO, histogramDTO);
    }


    private HistogramDTO getHistogramDTO(SubgroupDataSelectionDTO subgroupDataSelectionDTO, List<SubgroupDataDTO> scrpExtList, AnalysisChartConfigDTO analysisChartConfigDTO, HistogramConfigDTO histogramConfigDTO, HistogramDTO histogramDTO) {

        scrpExtList = chartCommonService.getTotalNumSubgroup(scrpExtList, subgroupDataSelectionDTO.getMaxNum(),2);
        /*通过测试数据重新组装子组*/
        List<SubgroupDataDTO> subgroupDataDTOArrayList = chartCommonService.reassembly(scrpExtList);
        /*当从聚合分析图表点入重新过滤*/
        if (analysisChartConfigDTO.getIsTopOne().equals(YesOrNoEnum.YES.getType())) {
            subgroupDataDTOArrayList = subgroupDataDTOArrayList.stream().filter(s ->
                    s.getF_PART().equals(subgroupDataSelectionDTO.getF_PART()) &&
                            s.getF_PRCS().equals(subgroupDataSelectionDTO.getF_PRCS()) &&
                            s.getF_TEST().equals(subgroupDataSelectionDTO.getF_TEST())
            ).collect(Collectors.toList());
        }

        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(subgroupDataDTOArrayList, analysisChartConfigDTO.getType(), histogramConfigDTO.getShortSdTermType());

        /*获取公差限数据*/
        SPEC_INF_DTO specInfDto = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTOArrayList.get(0));
        /*对scrpExtList的F_VAL字段做升序排列*/
        List<Double> doubles = dataSummaryDTO.getValList().stream().sorted(Comparator.comparing(Double::doubleValue)).collect(Collectors.toList());
        Double v = 0d;
        switch (STANDARDIZE_TYPEEnum.getType(histogramConfigDTO.getDataProcessType())) {
            case NULL:
                break;
            case TARGET_VAL:
                List<Double> arrayList = new ArrayList<>();
                for (Double aDouble : doubles) {
                    arrayList.add(BigDecimal.valueOf(aDouble).subtract(BigDecimal.valueOf(specInfDto.getF_TAR())).doubleValue());
                }
                doubles = arrayList;
                v = specInfDto.getF_TAR();
                break;
            case NOMINAL_VAL:
                if (specInfDto.getF_USL() == null || specInfDto.getF_LSL() == null)
                    break;
                v = (specInfDto.getF_USL() + specInfDto.getF_LSL()) / 2;
                List<Double> list = new ArrayList<>();
                for (Double aDouble : doubles) {
                    list.add(BigDecimal.valueOf(aDouble).subtract(BigDecimal.valueOf(v)).doubleValue());
                }
                doubles = list;
                break;
        }

        Map<String, Object> histogram = JohnSonConversionBaseUtil.Histogram(doubles, histogramPath);
        histogramDTO.setParams(histogram);

        TransformedResult johnson = JohnSonConversionBaseUtil.johnson(doubles, histogramConfigDTO.getDataTransformType(), johnsonPath);
        if (johnson != null) {
            doubles = johnson.getTransformedValues();
            histogramDTO.setTransformedResult(johnson);
            dataSummaryDTO.setUsl(dataSummaryDTO.getUsl() == null ? null :
                    BigDecimal.valueOf(dataSummaryDTO.getUsl()).subtract(BigDecimal.valueOf(v)).doubleValue());
            dataSummaryDTO.setLsl(dataSummaryDTO.getLsl() == null ? null :
                    BigDecimal.valueOf(dataSummaryDTO.getLsl()).subtract(BigDecimal.valueOf(v)).doubleValue());
            dataSummaryDTO.setTargetValue(dataSummaryDTO.getTargetValue() == null ? null :
                    BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).subtract(BigDecimal.valueOf(v)).doubleValue());
            dataSummaryDTO.setMax(doubles.stream().max(Double::compareTo).orElse(0d));
            dataSummaryDTO.setMin(doubles.stream().min(Double::compareTo).orElse(0d));
            dataSummaryDTO.setMean(doubles.stream().mapToDouble(Double::doubleValue).average().orElse(0d));
        }

        /*获取目标和超公差数据*/
        DataSummaryDTO.getBasicTwo(dataSummaryDTO);

        /*获取过程潜力指数*/
        DataSummaryDTO.getBasicThree(dataSummaryDTO);

        /*获取过程能力指数*/
        DataSummaryDTO.getBasicFour(dataSummaryDTO);

        /*获取拟合曲线*/
        histogramDTO.setSubgroupDataDTO(subgroupDataDTOArrayList.get(0));
        histogramDTO.setFrequency(HistogramUtil.getBinNum(dataSummaryDTO.getTestNum()));
        histogramDTO.setGroupInterval(HistogramUtil.getGroupInterval(dataSummaryDTO.getMax(), dataSummaryDTO.getMin(), histogramDTO.getFrequency()));
        histogramDTO.setGroupStart(HistogramUtil.getGroupStart(dataSummaryDTO.getMin(), histogramDTO.getGroupInterval()));
        /*计算完groupStart的值之后，把Frequency的值加1*/
        histogramDTO.setFrequency(histogramDTO.getFrequency() + 1);
        histogramDTO.setValList(doubles);
        double max;
        if (histogramConfigDTO.getXAxisValueMax() != null) {
            max = histogramConfigDTO.getXAxisValueMax();
        } else {
            max = dataSummaryDTO.getMean() + (histogramConfigDTO.getXAxisValueStd() == null ? 6 : histogramConfigDTO.getXAxisValueStd()) * dataSummaryDTO.getLongTermStandardDeviation();
        }
        double min;
        if (histogramConfigDTO.getXAxisValueMin() != null) {
            min = histogramConfigDTO.getXAxisValueMin();
        } else {
            min = dataSummaryDTO.getMean() - (histogramConfigDTO.getXAxisValueStd() == null ? 6 : histogramConfigDTO.getXAxisValueStd()) * dataSummaryDTO.getLongTermStandardDeviation();
        }
        if (histogramConfigDTO.getCurveType() == 0) {
            if (dataSummaryDTO.getShortTermStandardDeviation() != null && dataSummaryDTO.getShortTermStandardDeviation() > 0d)
                histogramDTO.setCurveMap(HistogramUtil.getNormalDistributionCurve(min,
                        max,
                        histogramConfigDTO.getPointNum(),
                        dataSummaryDTO.getMean(),
                        dataSummaryDTO.getShortTermStandardDeviation()));
        } else {
//            histogramDTO.setCurveMap(HistogramUtil.getNuclearDensityCurve(doubles, dataSummaryDTO.getMin(),
//                    dataSummaryDTO.getMax(), dataSummaryDTO.getMean()));
        }
        histogramDTO.setDataSummary(dataSummaryDTO);
        return histogramDTO;
    }

    @Override
    public List<HistogramDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.HISTOGRAM);

        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
        Integer isTopOne = analysisChartConfigDTO.getIsTopOne();

        HistogramConfigDTO histogramConfigDTO;
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            histogramConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), HistogramConfigDTO.class);
        } else {
            histogramConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), HistogramConfigDTO.class);
        }

        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        List<SubgroupDataDTO> sgrpExtList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);

        if (CollectionUtils.isEmpty(sgrpExtList)) {
            return Collections.emptyList();
        }



        subgroupDataSelectionDTO.setTestList(
                subgroupDataSelectionDTO.getTestList().stream()
                        .filter(ftest -> {
                            /*获取测试是否为变量类型*/
                            TEST_INF testInf = testInfService.getById(ftest);
                            return testInf != null && testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.VARIABLE.getType());
                        })
                        .collect(Collectors.toList())
        );

        /*多测试拆分*/
        sgrpExtList = chartCommonService.reassembly(sgrpExtList);
        /*将失效子组排除*/
        sgrpExtList = sgrpExtList.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sgrpExtList)) {
            return Collections.emptyList();
        }

        final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                subgroupDataSelectionDTO.getPartList(),
                subgroupDataSelectionDTO.getPtrvList(),
                subgroupDataSelectionDTO.getPrcsList(),
                subgroupDataSelectionDTO.getTestList());
        List<HistogramDTO> list = new ArrayList<>();

        List<Future<HistogramDTO>> futures = new ArrayList<>();
        for (Map<String, Long> map : queryMapList) {
            // 进行空值检查
            if (map == null) {
                continue;
            }
            Long fpart = map.get(Constants.part);
            Long fptrv = map.get(Constants.ptrv);
            Long fprcs = map.get(Constants.prcs);
            Long ftest = map.get(Constants.test);
            // 过滤符合条件的 SubgroupDataDTO 列表
            final List<SubgroupDataDTO> collect = sgrpExtList.stream()
                    .filter(x -> fpart != null && fpart.equals(x.getF_PART()) &&
                            fptrv != null && fptrv.equals(x.getF_REV()) &&
                            fprcs != null && fprcs.equals(x.getF_PRCS()) &&
                            ftest != null && ftest.equals(x.getF_TEST()))
                    .collect(Collectors.toList());

            // 当 collect 列表为空时，跳过当前循环
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }

            subgroupDataSelectionDTO.setF_PART(fpart).setF_REV(fptrv).setF_PRCS(fprcs).setF_TEST(ftest);

            // 提交任务到线程池
            SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
            futures.add(threadPoolConfig.threadPoolTaskExecutor().submit(() -> getHistogramDTO(finalSubgroupDataSelectionDTO, collect, analysisChartConfigDTO, histogramConfigDTO, new HistogramDTO())));
        }
        // 获取任务结果
        for (Future<HistogramDTO> future : futures) {
            try {
                HistogramDTO dto = future.get();
                if (dto != null) {
                    list.add(dto);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程处理出错", e);
            }
        }
        return list;
    }


    /**
     * 获取自定义查询条件
     *
     * @param subgroupDataSelectionDTO 自定义查询条件
     * @param type                     0:异常数据 1:控制图和直方图和查看数据等
     * @return
     */
    public SubgroupDataSelectionDTO getDataSelection(SubgroupDataSelectionDTO subgroupDataSelectionDTO, Integer type, AnalysisChartConfigDTO analysisChartConfigDTO) {
        if (StringUtils.isNotEmpty(subgroupDataSelectionDTO.getParameterId())) {
            PARAMETER_SET_INF_DTO parameterSetInfDto = parameterSetInfService.info(subgroupDataSelectionDTO.getParameterId());
            JudgeUtils.isNull(parameterSetInfDto, DataManagementExceptionEnum.PARAMETER_SET_NOT_EXISTS);

            Map<String, List<Long>> map = parameterSetInfService.getSearchCondition(parameterSetInfDto.getParameterChildDtoList());
            parameterSetInfDto.setMap(map);
            List<Long> F_TESTList = map.get(Constants.testList) == null ? new ArrayList<>() :
                    map.get(Constants.testList);
            List<Long> F_PARTList = map.get(Constants.partList) == null ? new ArrayList<>() :
                    map.get(Constants.partList);
            List<Long> F_PRCSList = map.get(Constants.prcsList) == null ? new ArrayList<>() :
                    map.get(Constants.prcsList);
            if (type == 1) {
                extracted(subgroupDataSelectionDTO, F_TESTList, F_PARTList, F_PRCSList, analysisChartConfigDTO);
            } else {
                if (Objects.isNull(subgroupDataSelectionDTO.getF_PART())) {
                    if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList()))
                        subgroupDataSelectionDTO.setPartList(F_PARTList);
                }
                if (Objects.isNull(subgroupDataSelectionDTO.getF_PRCS())) {
                    if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList()))
                        subgroupDataSelectionDTO.setPrcsList(F_PRCSList);
                }
                if (Objects.isNull(subgroupDataSelectionDTO.getF_TEST())) {
                    if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList()))
                        subgroupDataSelectionDTO.setTestList(F_TESTList);
                }
            }

            subgroupDataSelectionDTO = SubgroupDataSelectionDTO.getData(subgroupDataSelectionDTO, parameterSetInfDto);
        } else {
            if (type == 1 && CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getTestList()) && CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getPartList()) && CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getPrcsList())) {
                extracted(subgroupDataSelectionDTO, subgroupDataSelectionDTO.getTestList(), subgroupDataSelectionDTO.getPartList(), subgroupDataSelectionDTO.getPrcsList(), analysisChartConfigDTO);
            }
        }
        return subgroupDataSelectionDTO;
    }

    private void extracted(SubgroupDataSelectionDTO subgroupDataSelectionDTO, List<Long> F_TESTList, List<Long> F_PARTList, List<Long> F_PRCSList, AnalysisChartConfigDTO analysisChartConfigDTO) {
        /*控制图和直方图的测试条件只有一个*/
        SubgroupDataSelectionDTO sgrp_ext = new SubgroupDataSelectionDTO();
        if (StringUtils.isNotEmpty(subgroupDataSelectionDTO.getF_TEST())) {
            sgrp_ext.setF_TEST(subgroupDataSelectionDTO.getF_TEST());
        } else {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList())) {
                sgrp_ext.setTestList(F_TESTList);
            } else {
                sgrp_ext.setTestList(subgroupDataSelectionDTO.getTestList());
            }
        }
        if (StringUtils.isNotEmpty(subgroupDataSelectionDTO.getF_PART())) {
            sgrp_ext.setF_PART(subgroupDataSelectionDTO.getF_PART());
        } else {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList())) {
                sgrp_ext.setPartList(F_PARTList);
            } else {
                sgrp_ext.setPartList(subgroupDataSelectionDTO.getPartList());
            }
        }
        if (StringUtils.isNotEmpty(subgroupDataSelectionDTO.getF_PRCS())) {
            sgrp_ext.setF_PRCS(subgroupDataSelectionDTO.getF_PRCS());
        } else {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList())) {
                sgrp_ext.setPrcsList(F_PRCSList);
            } else {
                sgrp_ext.setPrcsList(subgroupDataSelectionDTO.getPrcsList());
            }
        }
        sgrp_ext.setF_FLAG(subgroupDataSelectionDTO.getF_FLAG());
        sgrp_ext.setDbType(subgroupDataSelectionDTO.getDbType());
        /*过程和产品拿查询出来的数据中最新的过程和产品*/
        if (analysisChartConfigDTO.getIsTopOne() == 0) {
            SubgroupDataDTO topOne = sgrpInfService.getTopOne(sgrp_ext);
            if (topOne != null) {
                if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_PART()))
                    subgroupDataSelectionDTO.setF_PART(topOne.getF_PART());
                if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_REV()))
                    subgroupDataSelectionDTO.setF_REV(topOne.getF_REV());
                if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_PRCS()))
                    subgroupDataSelectionDTO.setF_PRCS(topOne.getF_PRCS());
                if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_TEST())) {
                    LambdaQueryWrapper<SGRP_VAL> queryWrapper = new LambdaQueryWrapper<>();
                    if(!CollectionUtils.isEmpty(sgrp_ext.getTestList())){
                        queryWrapper.in(SGRP_VAL::getF_TEST, sgrp_ext.getTestList());
                    }
                    queryWrapper.eq(SGRP_VAL::getF_SGRP, topOne.getF_SGRP()).orderByDesc(SGRP_VAL::getF_SGTM);
                    List<SGRP_VAL> sgrpValList = sgrpValService.list(queryWrapper);
                    if (CollectionUtils.isNotEmpty(sgrpValList)) {
                        if (sgrp_ext.getTestList().size() == 1) {
                            subgroupDataSelectionDTO.setF_TEST(sgrp_ext.getTestList().get(0));
                        } else {
                            subgroupDataSelectionDTO.setF_TEST(sgrpValList.get(0).getF_TEST());
                        }
                    }
                }
            }
        } else {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList())) {
                subgroupDataSelectionDTO.setTestList(F_TESTList);
            }
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList())) {
                subgroupDataSelectionDTO.setPartList(F_PARTList);
            }
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList())) {
                subgroupDataSelectionDTO.setPrcsList(F_PRCSList);
            }
        }
    }


}

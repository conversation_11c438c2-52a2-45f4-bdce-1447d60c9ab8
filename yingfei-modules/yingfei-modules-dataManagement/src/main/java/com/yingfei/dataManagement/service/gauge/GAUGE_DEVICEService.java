package com.yingfei.dataManagement.service.gauge;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.GAUGE_DEVICE;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.GAUGE_DEVICE_DTO;
import com.yingfei.entity.vo.GAUGE_DEVICE_VO;

/**
* 
* @description 针对表【GAUGE_DEVICE(量具设备配置表)】的数据库操作Service
* @createDate 2024-07-30 11:19:11
*/
public interface GAUGE_DEVICEService extends IService<GAUGE_DEVICE>, BaseService<GAUGE_DEVICE_VO, GAUGE_DEVICE_DTO> {

    GAUGE_DEVICE_DTO info(Long id);
}

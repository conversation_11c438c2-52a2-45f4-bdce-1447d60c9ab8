package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.HistogramService;
import com.yingfei.dataManagement.service.chart.QuadrantalDiagramService;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.QuadrantalDiagramConfigDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QuadrantalDiagramServiceImpl implements QuadrantalDiagramService {

    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private HistogramService histogramService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private ChartCommonService chartCommonService;

    @Override
    public List<QuadrantalDiagramDTO> getQuadrantalDiagramInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.QUADRANTAL_DIAGRAM);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.QUADRANT_CHART_CONFIG_NOT_EXISTS);
        }
        QuadrantalDiagramConfigDTO quadrantalDiagramConfigDTO = new QuadrantalDiagramConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            quadrantalDiagramConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), QuadrantalDiagramConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            quadrantalDiagramConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), QuadrantalDiagramConfigDTO.class);
        }

        List<QuadrantalDiagramDTO> quadrantalDiagramDTOList = new ArrayList<>();
//        subgroupDataSelectionDTO = histogramService.getDataSelection(subgroupDataSelectionDTO, 0, analysisChartConfigDTO);
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return quadrantalDiagramDTOList;


        /*能力象限图只查询测试类型为变量的*/
        subgroupDataSelectionDTO.setTestType(YesOrNoEnum.YES.getType());

        /*获取子组测试数量*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return quadrantalDiagramDTOList;
        }
     //   subgroupDataDTOList = chartCommonService.getTotalNumSubgroup(subgroupDataDTOList, subgroupDataSelectionDTO.getMaxNum(),2);

        /*通过测试数据重新组装子组*/
        List<SubgroupDataDTO> reassemblySubgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        if (subgroupDataSelectionDTO.getTestType() == 1) {
            reassemblySubgroupDataDTOList = reassemblySubgroupDataDTOList.stream().filter(s -> s.getTestType() == 1).collect(Collectors.toList());
        }

        /*将part,prcs,test作为key生成map*/
        Map<String, List<SubgroupDataDTO>> map = reassemblySubgroupDataDTOList.stream()
                .collect(Collectors.groupingBy(s ->
                                s.getF_PART() + Constants.COMMA +
                                s.getF_REV() + Constants.COMMA +
                                s.getF_PRCS() + Constants.COMMA +
                                s.getF_TEST(),
                        LinkedHashMap::new, Collectors.toList()));

        SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
        map.forEach((k, v) -> {
            /*能力仪表盘*/
            v.sort(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP));
            /*将失效子组排除*/
            List<SubgroupDataDTO> collect = v.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());
            QuadrantalDiagramDTO quadrantalDiagramDTO = getQuadrantalDiagramDTO(collect, finalSubgroupDataSelectionDTO, analysisChartConfigDTO);
            quadrantalDiagramDTOList.add(quadrantalDiagramDTO);
        });
        return quadrantalDiagramDTOList;
    }

    private QuadrantalDiagramDTO getQuadrantalDiagramDTO(List<SubgroupDataDTO> subgroupDataSelectionDTOList, SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {
        if (CollectionUtils.isEmpty(subgroupDataSelectionDTOList)) {
            return null;
        }
        SubgroupDataDTO subgroupDataDTO = subgroupDataSelectionDTOList.get(0);
        QuadrantalDiagramDTO quadrantalDiagramDTO = new QuadrantalDiagramDTO();

        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(subgroupDataSelectionDTOList, analysisChartConfigDTO.getType(), 0);
        /*获取公差限数据  目标cp和目标cpk*/
        SPEC_INF_DTO specLim = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTO);
        if (specLim == null) {
            quadrantalDiagramDTO.setDataSummaryDTO(dataSummaryDTO);
            return quadrantalDiagramDTO;
        }
        /*获取目标和超公差数据*/
        DataSummaryDTO.getBasicTwo(dataSummaryDTO);

        /*获取过程潜力指数*/
        DataSummaryDTO.getBasicThree(dataSummaryDTO);

        /*获取过程能力指数*/
        DataSummaryDTO.getBasicFour(dataSummaryDTO);
        if (dataSummaryDTO.getCp() == null || specLim.getF_CP() == null || specLim.getF_CP() == 0d) {
            quadrantalDiagramDTO.setCp(0d);
        } else {
            quadrantalDiagramDTO.setCp(dataSummaryDTO.getCp() / specLim.getF_CP());
        }
        if (dataSummaryDTO.getCpk() == null || specLim.getF_CPK() == null || specLim.getF_CPK() == 0d) {
            quadrantalDiagramDTO.setCpk(0d);
        } else {
            quadrantalDiagramDTO.setCpk(dataSummaryDTO.getCpk() / specLim.getF_CPK());
        }
        quadrantalDiagramDTO.setDataSummaryDTO(dataSummaryDTO);
        quadrantalDiagramDTO.setF_PART(subgroupDataDTO.getF_PART());
        quadrantalDiagramDTO.setF_PTRV(subgroupDataDTO.getF_REV());
        quadrantalDiagramDTO.setF_PRCS(subgroupDataDTO.getF_PRCS());
        quadrantalDiagramDTO.setF_TEST(subgroupDataDTO.getF_TEST());
        quadrantalDiagramDTO.setF_PARTName(subgroupDataDTO.getPartName());
        quadrantalDiagramDTO.setF_PTRVName(subgroupDataDTO.getPtrvName());
        quadrantalDiagramDTO.setF_PRCSName(subgroupDataDTO.getPrcsName());
        quadrantalDiagramDTO.setF_TESTName(subgroupDataDTO.getTestName());
        return quadrantalDiagramDTO;
    }
}

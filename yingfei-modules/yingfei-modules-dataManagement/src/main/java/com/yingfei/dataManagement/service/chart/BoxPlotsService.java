package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.BoxPlotsConfigDTO;
import com.yingfei.entity.dto.chart.BoxPlotsDataDTO;
import com.yingfei.entity.enums.ChartTypeEnum;

import java.util.List;

public interface BoxPlotsService {
    List<BoxPlotsDataDTO> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO);
    List<BoxPlotsDataDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);
    void editConfig(Long menuId, String chartId,BoxPlotsConfigDTO boxPlotsConfigDTO, ChartTypeEnum chartTypeEnum);
}

package com.yingfei.dataManagement.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.SYSTEM_NOTIFICATION_INFMapper;
import com.yingfei.dataManagement.service.DEF_DATService;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.SYSTEM_NOTIFICATION_INFService;
import com.yingfei.entity.domain.SPEC_INF;
import com.yingfei.entity.domain.SYSTEM_NOTIFICATION_INF;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.vo.DEF_DAT_VO;
import com.yingfei.entity.vo.SYSTEM_NOTIFICATION_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SYSTEM_NOTIFICATION_INFServiceImpl extends ServiceImpl<SYSTEM_NOTIFICATION_INFMapper, SYSTEM_NOTIFICATION_INF> implements SYSTEM_NOTIFICATION_INFService {

    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private DEF_DATService defDatService;

    @Override
    public long getTotal(SYSTEM_NOTIFICATION_INF_VO systemNotificationInfVo) {
        return baseMapper.getTotal(systemNotificationInfVo);
    }

    @Override
    public List<SYSTEM_NOTIFICATION_INF_DTO> getList(SYSTEM_NOTIFICATION_INF_VO systemNotificationInfVo) {
        return baseMapper.getList(systemNotificationInfVo);
    }

    @Override
    public void add(SYSTEM_NOTIFICATION_INF_VO systemNotificationInfVo) {

    }

    @Override
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public void checkParam(SYSTEM_NOTIFICATION_INF_VO systemNotificationInfVo) {

    }

    @Override
    public void read(List<Long> ids) {
        LambdaUpdateWrapper<SYSTEM_NOTIFICATION_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SYSTEM_NOTIFICATION_INF::getF_SYNO, ids)
                .set(SYSTEM_NOTIFICATION_INF::getF_STATUS, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public SYSTEM_NOTIFICATION_INF_DTO info(Long id) {
        SYSTEM_NOTIFICATION_INF systemNotificationInf = baseMapper.selectById(id);
        SYSTEM_NOTIFICATION_INF_DTO systemNotificationInfDto = new SYSTEM_NOTIFICATION_INF_DTO();
        if (systemNotificationInf == null) return systemNotificationInfDto;
        BeanUtils.copyPropertiesIgnoreNull(systemNotificationInf, systemNotificationInfDto);
        if (StringUtils.isEmpty(systemNotificationInfDto.getF_DATA())) return systemNotificationInfDto;
        SYSTEM_NOTIFICATION_ALARM_DTO systemNotificationAlarmDto =
                JSONObject.parseObject(systemNotificationInfDto.getF_DATA(), SYSTEM_NOTIFICATION_ALARM_DTO.class);
        if (StringUtils.isNotEmpty(systemNotificationAlarmDto.getSgrpId())) {
            List<SubgroupDataDTO> subgroupDataDTOList = sgrpInfService.findById(systemNotificationAlarmDto.getSgrpId());
            if (CollectionUtils.isNotEmpty(subgroupDataDTOList))
                systemNotificationAlarmDto.setList(subgroupDataDTOList);
        } else {
            if (CollectionUtils.isNotEmpty(systemNotificationAlarmDto.getList())) {
                Set<Long> collect = systemNotificationAlarmDto.getList().stream().map(SubgroupDataDTO::getF_SGRP).collect(Collectors.toSet());
                SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
                subgroupDataSelectionDTO.setSgrpList(new ArrayList<>(collect));
                subgroupDataSelectionDTO.setF_FLAG(2);
                List<SubgroupDataDTO> subgroupDataDTOList = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
                systemNotificationAlarmDto.setList(subgroupDataDTOList);
            }
        }
        if (CollectionUtils.isNotEmpty(systemNotificationAlarmDto.getList())) {
            systemNotificationAlarmDto.getList().forEach(subgroupDataDTO -> {
                subgroupDataDTO.getSgrpValDtoList().forEach(sgrpValDto -> {
                    List<Long> defectIds = sgrpValDto.getSgrpValChildDto().getTestList().stream().map(SGRP_VAL_CHILD_DTO.Test::getDefectId)
                            .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(defectIds)) {
                        LinkedHashMap<Long, List<SGRP_VAL_CHILD_DTO.Test>> map = sgrpValDto.getSgrpValChildDto().getTestList().stream().collect(Collectors.groupingBy(SGRP_VAL_CHILD_DTO.Test::getDefectId,
                                LinkedHashMap::new, Collectors.toList()));
                        DEF_DAT_VO defDatVo = new DEF_DAT_VO();
                        defDatVo.setIds(defectIds);
                        defDatVo.setDbType(InitConfig.getDriverType());
                        List<DEF_DAT_DTO> list = defDatService.getList(defDatVo);
                        list.forEach(defDatDto -> {
                            List<SGRP_VAL_CHILD_DTO.Test> tests = map.get(defDatDto.getF_DEF());
                            defDatDto.setTestDefImg(tests.stream().map(SGRP_VAL_CHILD_DTO.Test::getImg).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
                        });
                        sgrpValDto.getSgrpValChildDto().setDefDatDtoList(list);
                    }
                });
            });

        }
        systemNotificationInfDto.setSystemNotificationAlarmDto(systemNotificationAlarmDto);
        return systemNotificationInfDto;
    }
}

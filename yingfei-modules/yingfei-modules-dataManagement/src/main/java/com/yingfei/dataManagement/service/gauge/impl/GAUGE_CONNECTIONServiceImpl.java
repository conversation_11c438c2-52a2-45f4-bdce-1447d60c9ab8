package com.yingfei.dataManagement.service.gauge.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.service.gauge.GAUGE_AGENTService;
import com.yingfei.dataManagement.service.gauge.GAUGE_CONNECTIONService;
import com.yingfei.dataManagement.service.gauge.GAUGE_DEVICEService;
import com.yingfei.dataManagement.service.gauge.GAUGE_INTERFACEService;
import com.yingfei.entity.domain.GAUGE_AGENT;
import com.yingfei.entity.domain.GAUGE_CONNECTION;
import com.yingfei.dataManagement.mapper.GAUGE_CONNECTIONMapper;
import com.yingfei.entity.domain.GAUGE_DEVICE;
import com.yingfei.entity.domain.GAUGE_INTERFACE;
import com.yingfei.entity.dto.GAUGE_AGENT_DTO;
import com.yingfei.entity.dto.GAUGE_CONNECTION_CONFIG_DTO;
import com.yingfei.entity.dto.GAUGE_CONNECTION_DTO;
import com.yingfei.entity.dto.GAUGE_INTERFACE_DTO;
import com.yingfei.entity.vo.GAUGE_CONNECTION_VO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 针对表【GAUGE_CONNECTION(量具连接参数表)】的数据库操作Service实现
 * @createDate 2024-07-30 11:19:06
 */
@Service
public class GAUGE_CONNECTIONServiceImpl extends ServiceImpl<GAUGE_CONNECTIONMapper, GAUGE_CONNECTION>
        implements GAUGE_CONNECTIONService {

    @Resource
    private GAUGE_DEVICEService gaugeDeviceService;

    @Resource
    private GAUGE_INTERFACEService gaugeInterfaceService;
    @Resource
    private GAUGE_AGENTService gaugeAgentService;

    @Override
    public long getTotal(GAUGE_CONNECTION_VO gaugeConnectionVo) {
        return 0;
    }

    @Override
    public List<GAUGE_CONNECTION_DTO> getList(GAUGE_CONNECTION_VO gaugeConnectionVo) {
        List<GAUGE_CONNECTION_DTO> list = baseMapper.getList(gaugeConnectionVo);
        list.forEach(gaugeConnectionDto -> {
            if (StringUtils.isNotEmpty(gaugeConnectionDto.getF_CONFIG())) {
                gaugeConnectionDto.setGaugeConnectionConfigDto(
                        JSONObject.parseObject(gaugeConnectionDto.getF_CONFIG(), GAUGE_CONNECTION_CONFIG_DTO.class));
            }
            GAUGE_INTERFACE gaugeInterface = gaugeInterfaceService.getById(gaugeConnectionDto.getF_GAIN());
            if (gaugeInterface != null) {
                GAUGE_INTERFACE_DTO gaugeInterfaceDto = new GAUGE_INTERFACE_DTO();
                BeanUtils.copyPropertiesIgnoreNull(gaugeInterface, gaugeInterfaceDto);
                gaugeConnectionDto.setGaugeInterfaceDto(gaugeInterfaceDto);
            }
        });
        return list;
    }

    @Override
    public void add(GAUGE_CONNECTION_VO gaugeConnectionVo) {
        GAUGE_CONNECTION gaugeConnection = new GAUGE_CONNECTION();
        BeanUtils.copyPropertiesIgnoreNull(gaugeConnectionVo, gaugeConnection);
        String jsonString = JSONObject.toJSONString(gaugeConnectionVo.getGaugeConnectionConfigDto());
        gaugeConnection.setF_CONFIG(jsonString);
        baseMapper.insert(gaugeConnection);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
        /*删除对应的量具设备*/
        LambdaQueryWrapper<GAUGE_DEVICE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GAUGE_DEVICE::getF_GICP, ids);
        gaugeDeviceService.remove(queryWrapper);
    }

    @Override
    public void checkParam(GAUGE_CONNECTION_VO gaugeConnectionVo) {
        if (StringUtils.isEmpty(gaugeConnectionVo.getF_GAIN()) || StringUtils.isEmpty(gaugeConnectionVo.getF_GAAG())
                || gaugeConnectionVo.getGaugeConnectionConfigDto() == null) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
    }

    @Override
    public GAUGE_CONNECTION_DTO info(Long id) {
        GAUGE_CONNECTION gaugeConnection = baseMapper.selectById(id);
        GAUGE_CONNECTION_DTO gaugeConnectionDto = new GAUGE_CONNECTION_DTO();
        BeanUtils.copyPropertiesIgnoreNull(gaugeConnection, gaugeConnectionDto);
        if (StringUtils.isNotEmpty(gaugeConnectionDto.getF_CONFIG())) {
            GAUGE_CONNECTION_CONFIG_DTO gaugeConnectionConfigDto =
                    JSONObject.parseObject(gaugeConnectionDto.getF_CONFIG(), GAUGE_CONNECTION_CONFIG_DTO.class);
            gaugeConnectionDto.setGaugeConnectionConfigDto(gaugeConnectionConfigDto);
        }
        GAUGE_INTERFACE gaugeInterface = gaugeInterfaceService.getById(gaugeConnectionDto.getF_GAIN());
        if (gaugeInterface != null) {
            GAUGE_INTERFACE_DTO gaugeInterfaceDto = new GAUGE_INTERFACE_DTO();
            BeanUtils.copyPropertiesIgnoreNull(gaugeInterface, gaugeInterfaceDto);
            gaugeConnectionDto.setGaugeInterfaceDto(gaugeInterfaceDto);
        }
        GAUGE_AGENT gaugeAgent = gaugeAgentService.getById(gaugeConnectionDto.getF_GAAG());
        if (gaugeAgent != null) {
            GAUGE_AGENT_DTO gaugeAgentDto = new GAUGE_AGENT_DTO();
            BeanUtils.copyPropertiesIgnoreNull(gaugeAgent, gaugeAgentDto);
            gaugeConnectionDto.setGaugeAgentDto(gaugeAgentDto);
        }
        return gaugeConnectionDto;
    }
}





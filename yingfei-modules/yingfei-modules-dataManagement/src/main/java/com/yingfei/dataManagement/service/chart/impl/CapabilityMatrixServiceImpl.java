package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.chart.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.*;
import com.yingfei.entity.enums.ChartTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CapabilityMatrixServiceImpl implements CapabilityMatrixService {

    @Resource
    private RedisService redisService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private HistogramService histogramService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private InstrumentBoardService instrumentBoardService;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private ChartCommonService chartCommonService;

    private static final ReentrantLock lock = new ReentrantLock();

    @Override
    public List<CapabilityMatrixDTO> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.BOX_PLOTS);

        CapabilityMatrixConfigDTO capabilityMatrixConfigDTO = new CapabilityMatrixConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            capabilityMatrixConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), CapabilityMatrixConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            capabilityMatrixConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), CapabilityMatrixConfigDTO.class);
        }

        /*能力矩阵列表*/
        List<CapabilityMatrixDTO> list = new ArrayList<>();
        /*获取缓存查询条件*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return list;

        /*能力矩阵只查询测试类型为变量的*/
        subgroupDataSelectionDTO.setTestType(YesOrNoEnum.YES.getType());
        /*获取缓存子组*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return list;
        /*通过测试数据重新组装子组*/
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);

        if (subgroupDataSelectionDTO.getTestType() == 1) {
            subgroupDataDTOList = subgroupDataDTOList.stream().filter(s -> s.getTestType() == 1).collect(Collectors.toList());
        }

        /*将part,prcs,test作为key生成map*/
        Map<String, List<SubgroupDataDTO>> map = subgroupDataDTOList.stream()
                .collect(Collectors.groupingBy(s ->
                                s.getF_PART() + Constants.COMMA +
                                        s.getF_REV() + Constants.COMMA +
                                        s.getF_PRCS() + Constants.COMMA +
                                        s.getF_TEST(),
                        LinkedHashMap::new, Collectors.toList()));


        CountDownLatch countDownLatch = new CountDownLatch(map.size());

        SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
        CapabilityMatrixConfigDTO finalCapabilityMatrixConfigDTO = capabilityMatrixConfigDTO;
        map.forEach((k, v) -> {
            threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
                try {
                    lock.lock();
                    /*将失效子组排除*/
                    List<SubgroupDataDTO> collect = v.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType())
                            .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP))
                            .collect(Collectors.toList());
                    /*能力仪表盘*/
                    CapabilityMatrixDTO capabilityMatrixDTO = getCapabilityMatrix(collect, finalSubgroupDataSelectionDTO, analysisChartConfigDTO, finalCapabilityMatrixConfigDTO);
                    if (capabilityMatrixDTO != null) {
                        capabilityMatrixDTO.setStartTime(collect.get(0).getF_SGTM().getTime());
                        capabilityMatrixDTO.setEndTime(collect.get(collect.size() - 1).getF_SGTM().getTime());
                        list.add(capabilityMatrixDTO);
                    }
                } finally {
                    log.info("剩余数量 : {}", countDownLatch.getCount());
                    countDownLatch.countDown();
                    lock.unlock();
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 计算不同的part、pacs、test的仪表盘数据
     *
     * @param subgroupDataSelectionDTOList 列表
     * @return
     */
    @Nullable
    private CapabilityMatrixDTO getCapabilityMatrix(List<SubgroupDataDTO> subgroupDataSelectionDTOList, SubgroupDataSelectionDTO subgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO, CapabilityMatrixConfigDTO capabilityMatrixConfigDTO) {
        if (CollectionUtils.isEmpty(subgroupDataSelectionDTOList)) {
            return null;
        }
        SubgroupDataDTO subgroupDataDTO = subgroupDataSelectionDTOList.get(0);
        CapabilityMatrixDTO capabilityMatrixDTO = new CapabilityMatrixDTO(subgroupDataSelectionDTOList.get(0));

        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(subgroupDataSelectionDTOList, analysisChartConfigDTO.getType(), 0);

        /*获取公差限数据  目标cp和目标cpk*/
        SPEC_INF_DTO specLim = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTO);
        if (specLim == null) {
            capabilityMatrixDTO.setDataSummaryDTO(dataSummaryDTO);
            return capabilityMatrixDTO;
        }

        /*获取目标和超公差数据*/
        DataSummaryDTO.getBasicTwo(dataSummaryDTO);

        /*获取过程潜力指数*/
        DataSummaryDTO.getBasicThree(dataSummaryDTO);

        /*获取过程能力指数*/
        DataSummaryDTO.getBasicFour(dataSummaryDTO);

        if (dataSummaryDTO.getCp() == null || specLim.getF_CP() == null || specLim.getF_CP() == 0d) {
            capabilityMatrixDTO.setCpRatio(0d);
        } else {
            capabilityMatrixDTO.setCpRatio(dataSummaryDTO.getCp() / specLim.getF_CP());
        }
        if (dataSummaryDTO.getCpk() == null || specLim.getF_CPK() == null || specLim.getF_CPK() == 0d) {
            capabilityMatrixDTO.setCpkRatio(0d);
        } else {
            capabilityMatrixDTO.setCpkRatio(dataSummaryDTO.getCpk() / specLim.getF_CPK());
        }

        /*获取能力矩阵所需参数*/
        CapabilityMatrixPotentialExpectDTO capabilityMatrixPotentialExpectDTO = instrumentBoardService.competencyMatrix(dataSummaryDTO, capabilityMatrixConfigDTO);
        capabilityMatrixDTO.setPotential(capabilityMatrixPotentialExpectDTO.getPotential());
        capabilityMatrixDTO.setPotentialCenteredProcessBean(capabilityMatrixPotentialExpectDTO.getPotential_Centered_Process());
        capabilityMatrixDTO.setExpect(capabilityMatrixPotentialExpectDTO.getExpect());
        capabilityMatrixDTO.setGradingBean(capabilityMatrixPotentialExpectDTO.getGradingBean());
        capabilityMatrixDTO.setDataSummaryDTO(dataSummaryDTO);
        capabilityMatrixDTO.setPp(dataSummaryDTO.getPp());
        capabilityMatrixDTO.setPpk(dataSummaryDTO.getPpk());
        return capabilityMatrixDTO;
    }
}

package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.ExportImgWrapper;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface ChartCommonService {
    Map<String, Object> getPageSubgroup(SubgroupDataSelectionDTO subgroupDataSelectionDTO, Integer type);

    SubgroupDataSelectionDTO getCacheCondition(SubgroupDataSelectionDTO subgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO);

    List<SubgroupDataDTO> getCacheSubgroupData(SubgroupDataSelectionDTO subgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO);

    List<SubgroupDataDTO> reassembly(List<SubgroupDataDTO> subgroupDataDTOList);

    List<SubgroupDataDTO> testValReassembly(List<SubgroupDataDTO> subgroupDataDTOList);

    void exportExcel(String menuId, ExportImgWrapper exportImgWrapper, HttpServletResponse response);

    List<Map<String, Long>> generateCombinations(List<Long> partList, List<Long> ptrvList, List<Long> prcsList, List<Long> testList);
    /**
     * 根据配置获取最大返回子组数的子组数据
     * @param subgroupDataDTOList
     * @param maxNum
     * @return
     */
    List<SubgroupDataDTO> getTotalNumSubgroup(List<SubgroupDataDTO> subgroupDataDTOList, Integer maxNum,int sort);
}

package com.yingfei.dataManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.domain.SGRP_CMT;
import com.yingfei.entity.dto.SGRP_CMT_DTO;
import com.yingfei.entity.vo.SgrpCmtAddVO;
import com.yingfei.entity.vo.SgrpCmtUpdateVO;

import java.util.List;

/**
*
* @description 针对表【SGRP_CMT(子组备注表)】的数据库操作Service
* @createDate 2024-06-13 15:01:42
*/
public interface SGRP_CMTService extends IService<SGRP_CMT> {

    void add(SgrpCmtAddVO vo);

    void edit(SgrpCmtUpdateVO vo);

    List<SGRP_CMT_DTO> getBySgrp(Long fsgrp);

    void del(List<Long> fCmtList);

   List<SGRP_CMT_DTO> getBySgrpList(List<Long> sgrpList);
}

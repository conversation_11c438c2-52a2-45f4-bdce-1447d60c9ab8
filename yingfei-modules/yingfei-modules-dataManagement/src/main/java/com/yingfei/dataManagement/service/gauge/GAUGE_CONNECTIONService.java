package com.yingfei.dataManagement.service.gauge;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.GAUGE_CONNECTION;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.GAUGE_CONNECTION_DTO;
import com.yingfei.entity.vo.GAUGE_CONNECTION_VO;

/**
* 
* @description 针对表【GAUGE_CONNECTION(量具连接参数表)】的数据库操作Service
* @createDate 2024-07-30 11:19:06
*/
public interface GAUGE_CONNECTIONService extends IService<GAUGE_CONNECTION>, BaseService<GAUGE_CONNECTION_VO, GAUGE_CONNECTION_DTO> {

    GAUGE_CONNECTION_DTO info(Long id);
}

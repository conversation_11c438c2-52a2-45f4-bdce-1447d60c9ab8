package com.yingfei.dataManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.mapper.EMPL_INFMapper;
import com.yingfei.dataManagement.mapper.EMPL_RESPONSIBLE_INFMapper;
import com.yingfei.dataManagement.service.EMPL_RESPONSIBLE_INFService;
import com.yingfei.dataManagement.service.TEST_INFService;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.EMPL_INF;
import com.yingfei.entity.domain.EMPL_RESPONSIBLE_INF;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO;
import com.yingfei.entity.enums.EMPL_INFStatusEnum;
import com.yingfei.entity.vo.EMPL_RESPONSIBLE_INF_VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工责任信息Service实现类
 * <AUTHOR>
 * @description 针对表【EMPL_RESPONSIBLE_INF(员工责任信息表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class EMPL_RESPONSIBLE_INFServiceImpl extends ServiceImpl<EMPL_RESPONSIBLE_INFMapper, EMPL_RESPONSIBLE_INF>
        implements EMPL_RESPONSIBLE_INFService {
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private EMPL_INFMapper  emplInfMapper;

    @Override
    public long getTotal(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        return baseMapper.selectCount(buildQueryWrapper(emplResponsibleInfVo,true));
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getList(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        final List<EMPL_RESPONSIBLE_INF> emplResponsibleInfs = baseMapper.selectList(buildQueryWrapper(emplResponsibleInfVo, false));
        final List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = BeanUtil.copyToList(emplResponsibleInfs, EMPL_RESPONSIBLE_INF_DTO.class);
        buildTest(emplResponsibleInfDtos);
        buildEmpl(emplResponsibleInfDtos);
        return emplResponsibleInfDtos;
    }

    /**
     * 查询员工责任信息列表（分页）
     *
     * @param emplResponsibleInfVo
     * @return
     */
    @Override
    public TableDataInfo<EMPL_RESPONSIBLE_INF_DTO> getPageList(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo,false);
        Page<EMPL_RESPONSIBLE_INF> page = BaseEntity.convertToPage(emplResponsibleInfVo.getOffset(), emplResponsibleInfVo.getNext());
        IPage<EMPL_RESPONSIBLE_INF> emplResponsibleInfDtoiPage = baseMapper.selectPage(page,queryWrapper);
        final List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = BeanUtil.copyToList(emplResponsibleInfDtoiPage.getRecords(), EMPL_RESPONSIBLE_INF_DTO.class);
        buildTest(emplResponsibleInfDtos);
        buildEmpl(emplResponsibleInfDtos);
       return new TableDataInfo<EMPL_RESPONSIBLE_INF_DTO>(emplResponsibleInfDtos, emplResponsibleInfDtoiPage.getTotal());
    }

    private void buildTest(List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos) {
            if (CollectionUtils.isNotEmpty(emplResponsibleInfDtos)) {
                //k:F_RESP  v：F_TEST
//                final Map<Long, Long> fTestMap = getFTestMapValues(emplResponsibleInfDtos);
                Map<Long, List<Long>> fTestMap = emplResponsibleInfDtos.stream()
                        .collect(Collectors.toMap(
                                EMPL_RESPONSIBLE_INF_DTO::getF_RESP,
                                dto -> Arrays.stream(dto.getF_DATA().split(",")).map(Long::parseLong).collect(Collectors.toList()),
                                (existing, newVal) -> newVal
                        ));
                if (ObjectUtils.isNotEmpty(fTestMap)) {
                    //testId集合
                    final HashSet<Long> testIdSet = new HashSet<>();
                    for (List<Long> value : fTestMap.values()) {
                        testIdSet.addAll(value);
                    }

                    final LambdaQueryWrapper<TEST_INF> testInfLambdaQueryWrapper = new LambdaQueryWrapper<TEST_INF>()
                            .in(TEST_INF::getF_TEST, testIdSet)
                            .eq(TEST_INF::getF_DEL, DelFlagEnum.USE.getType());
                    final List<TEST_INF> testInfList = testInfService.getBaseMapper().selectList(testInfLambdaQueryWrapper);
                    if (CollectionUtils.isNotEmpty(testInfList)) {
                        //k:F_TEST  v：F_NAME
                        final Map<Long, String> testMap = testInfList.stream()
                                .collect(Collectors.toMap(
                                        TEST_INF::getF_TEST,
                                        TEST_INF::getF_NAME,
                                        (existing, replacement) -> existing
                                ));
                        for (EMPL_RESPONSIBLE_INF_DTO emplResponsibleInfDto : emplResponsibleInfDtos) {
                            if (ObjectUtils.isNotEmpty(fTestMap.get(emplResponsibleInfDto.getF_RESP()))) {
                                final List<Long> testIdList = fTestMap.get(emplResponsibleInfDto.getF_RESP());
                               List<EMPL_RESPONSIBLE_INF_DTO.Test> testList = new ArrayList<>();
                                for (Long testID : testIdList) {
                                    final String testName = testMap.get(testID);
                                    EMPL_RESPONSIBLE_INF_DTO.Test test = new EMPL_RESPONSIBLE_INF_DTO.Test();
                                    test.setF_TEST(testID);
                                    test.setTestName(testName);
                                    testList.add(test);
                                }
                                emplResponsibleInfDto.setTestList(testList);
                            }
                        }
                    }
                }
            }
        }

    private void buildEmpl(List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos) {
        if (CollectionUtils.isNotEmpty(emplResponsibleInfDtos)) {
            final List<Long> emplIdList = emplResponsibleInfDtos.stream().map(EMPL_RESPONSIBLE_INF_DTO::getF_EMPL).distinct().collect(Collectors.toList());
            final List<EMPL_INF> emplInfs = emplInfMapper.selectList(new LambdaQueryWrapper<EMPL_INF>().eq(EMPL_INF::getF_STATUS, EMPL_INFStatusEnum.ACTIVATE.getCode()).in(EMPL_INF::getF_EMPL, emplIdList));
           if(CollectionUtils.isNotEmpty(emplInfs)){
               final Map<Long, String> emplMap = emplInfs.stream().collect(Collectors.toMap(EMPL_INF::getF_EMPL, EMPL_INF::getF_NAME));
               for (EMPL_RESPONSIBLE_INF_DTO emplResponsibleInfDto : emplResponsibleInfDtos) {
                   emplResponsibleInfDto.setEmplName(emplMap.get(emplResponsibleInfDto.getF_EMPL()));
               }
           }
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        // 参数校验
        checkParam(emplResponsibleInfVo);

        EMPL_RESPONSIBLE_INF emplResponsibleInf = new EMPL_RESPONSIBLE_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplResponsibleInfVo, emplResponsibleInf);

        // 设置默认值
        emplResponsibleInf.setF_RESP(JudgeUtils.defaultIdentifierGenerator.nextId(null));
        emplResponsibleInf.setF_DEL(DelFlagEnum.USE.getType());
        emplResponsibleInf.setF_CRUE(SecurityUtils.getUserId());
        emplResponsibleInf.setF_CRTM(new Date());

        baseMapper.insert(emplResponsibleInf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_RESP())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        // 参数校验
        checkParam(emplResponsibleInfVo);

        EMPL_RESPONSIBLE_INF emplResponsibleInf = new EMPL_RESPONSIBLE_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplResponsibleInfVo, emplResponsibleInf);

        // 设置编辑信息
        emplResponsibleInf.setF_EDUE(SecurityUtils.getUserId());
        emplResponsibleInf.setF_EDTM(new Date());

        baseMapper.updateById(emplResponsibleInf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        LambdaUpdateWrapper<EMPL_RESPONSIBLE_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(EMPL_RESPONSIBLE_INF::getF_RESP, ids);
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.DELETE.getType());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDUE, SecurityUtils.getUserId());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDTM, LocalDateTime.now());

        baseMapper.update(null, updateWrapper);
    }
@Override
    public void checkParam(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_TYPE())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_EMPL())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if (StringUtils.isBlank(emplResponsibleInfVo.getF_DATA())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
    }



    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getByFEmpl(Long fEmpl) {
        if (ObjectUtils.isEmpty(fEmpl)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        final EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo = new EMPL_RESPONSIBLE_INF_VO();
        emplResponsibleInfVo.setF_EMPL(fEmpl);
        emplResponsibleInfVo.setF_TYPE(0);
        LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo,false);
        final List<EMPL_RESPONSIBLE_INF> emplResponsibleInfs = baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(emplResponsibleInfs)) {
            final List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = BeanUtil.copyToList(emplResponsibleInfs, EMPL_RESPONSIBLE_INF_DTO.class);
            buildTest(emplResponsibleInfDtos);
            buildEmpl(emplResponsibleInfDtos);
            return emplResponsibleInfDtos;
        }
        return Collections.emptyList();
    }


    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getByFTest(Long fTest) {
        if (ObjectUtils.isEmpty(fTest)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        final EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo = new EMPL_RESPONSIBLE_INF_VO();
        emplResponsibleInfVo.setTestIds(Collections.singletonList(fTest));
        emplResponsibleInfVo.setF_TYPE(0);
        LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = buildQueryWrapper(emplResponsibleInfVo,false);
        final List<EMPL_RESPONSIBLE_INF> emplResponsibleInfs = baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(emplResponsibleInfs)) {
            final List<EMPL_RESPONSIBLE_INF_DTO> emplResponsibleInfDtos = BeanUtil.copyToList(emplResponsibleInfs, EMPL_RESPONSIBLE_INF_DTO.class);
            buildTest(emplResponsibleInfDtos);
            buildEmpl(emplResponsibleInfDtos);
            return emplResponsibleInfDtos;
        }
        return Collections.emptyList();
    }



    /**
     * 构建查询条件
     * @param emplResponsibleInfVo 查询条件
     * @return 查询包装器
     */
    private LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> buildQueryWrapper(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo,Boolean isCount) {
        LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = new LambdaQueryWrapper<>();

        // 默认查询未删除的记录
            queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.USE.getType());

        // 业务类型查询
        if (ObjectUtils.isNotEmpty(emplResponsibleInfVo.getF_TYPE())) {
            queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_TYPE, emplResponsibleInfVo.getF_TYPE());
        }

        // 员工ID查询
        if (ObjectUtils.isNotEmpty(emplResponsibleInfVo.getF_EMPL())) {
            queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_EMPL, emplResponsibleInfVo.getF_EMPL());
        }

        // 员工ID列表查询
        if (CollectionUtils.isNotEmpty(emplResponsibleInfVo.getEmplIds())) {
            queryWrapper.in(EMPL_RESPONSIBLE_INF::getF_EMPL, emplResponsibleInfVo.getEmplIds());
        }

        // 测试ID列表查询（F_DATA直接存储testId）
        if (CollectionUtils.isNotEmpty(emplResponsibleInfVo.getTestIds())) {
            List<String> testIds = emplResponsibleInfVo.getTestIds().stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());

            // 确认testIds不为空
            if (!CollectionUtils.isEmpty(testIds)) {
                queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_TYPE, 0);
                // 直接对F_DATA字段进行IN查询，无需JSON解析
                final Object[] testIdArray = testIds.toArray();
                // 一个测试只能有一个负责人，使用OR关系匹配数组中的任一测试ID
                if (testIdArray.length > 0) {
                    // 开启第一个条件分组
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < testIdArray.length; i++) {
                            String testId = testIdArray[i]+"";
                            if (i == 0) {
                                // 第一个条件直接添加
                                wrapper.like(EMPL_RESPONSIBLE_INF::getF_DATA, testId);
                            } else {
                                // 后续条件使用OR连接
                                wrapper.or().like(EMPL_RESPONSIBLE_INF::getF_DATA, testId);
                            }
                        }
                    });
                }
            }
        }

        // 数据内容模糊查询
        if (StringUtils.isNotBlank(emplResponsibleInfVo.getF_DATA())) {
            queryWrapper.like(EMPL_RESPONSIBLE_INF::getF_DATA, emplResponsibleInfVo.getF_DATA());
        }
        return queryWrapper;
    }


}

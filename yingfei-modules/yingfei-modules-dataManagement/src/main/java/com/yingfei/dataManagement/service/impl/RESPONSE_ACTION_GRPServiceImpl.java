package com.yingfei.dataManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.RESPONSE_ACTION_GRPMapper;
import com.yingfei.dataManagement.service.RESPONSE_ACTION_DATService;
import com.yingfei.dataManagement.service.RESPONSE_ACTION_GRPService;
import com.yingfei.entity.domain.RESPONSE_ACTION_DAT;
import com.yingfei.entity.domain.RESPONSE_ACTION_GRP;
import com.yingfei.entity.dto.RESPONSE_ACTION_GRP_DTO;
import com.yingfei.entity.vo.RESPONSE_ACTION_GRP_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【RESPONSE_ACTION_GRP(储存改善措施组信息表)】的数据库操作Service实现
* @createDate 2024-05-08 16:28:21
*/
@Service
public class RESPONSE_ACTION_GRPServiceImpl extends ServiceImpl<RESPONSE_ACTION_GRPMapper, RESPONSE_ACTION_GRP>
    implements RESPONSE_ACTION_GRPService {

    @Resource
    private RESPONSE_ACTION_DATService responseActionDatService;
    
    @Override
    public long getTotal(RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        return baseMapper.getTotal(responseActionGrpVo);
    }

    @Override
    public List<RESPONSE_ACTION_GRP_DTO> getList(RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        return baseMapper.getList(responseActionGrpVo);
    }

    @Override
    public void add(RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        RESPONSE_ACTION_GRP responseActionGrp = new RESPONSE_ACTION_GRP();
        BeanUtils.copyPropertiesIgnoreNull(responseActionGrpVo,responseActionGrp);
        baseMapper.insert(responseActionGrp);
    }

    @Override
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        /*判断关联数据是否存在*/
        LambdaQueryWrapper<RESPONSE_ACTION_DAT> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RESPONSE_ACTION_DAT::getF_RAGP, ids).eq(RESPONSE_ACTION_DAT::getF_DEL, DelFlagEnum.USE.getType());
        List<RESPONSE_ACTION_DAT> list = responseActionDatService.list(wrapper);
        if (CollectionUtils.isNotEmpty(list))
            throw new BusinessException(CommonExceptionEnum.PRESENCE_SUBDATA__EXCEPTION);

        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<RESPONSE_ACTION_GRP> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(RESPONSE_ACTION_GRP::getF_RAGP, ids)
                .set(RESPONSE_ACTION_GRP::getF_DEL, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void checkParam(RESPONSE_ACTION_GRP_VO responseActionGrpVo) {
        LambdaQueryWrapper<RESPONSE_ACTION_GRP> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RESPONSE_ACTION_GRP::getF_NAME, responseActionGrpVo.getF_NAME()).eq(RESPONSE_ACTION_GRP::getF_DEL, DelFlagEnum.USE.getType());
        List<RESPONSE_ACTION_GRP> responseActionGrpList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(responseActionGrpList)) return;
        if (StringUtils.isNotEmpty(responseActionGrpVo.getF_RAGP())) {
            if (responseActionGrpList.size() > 1 || !Objects.equals(responseActionGrpList.get(0).getF_RAGP(), responseActionGrpVo.getF_RAGP())) {
                throw new BusinessException(DataManagementExceptionEnum.RESPONSE_ACTION_GRP_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (responseActionGrpList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.RESPONSE_ACTION_GRP_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }
}





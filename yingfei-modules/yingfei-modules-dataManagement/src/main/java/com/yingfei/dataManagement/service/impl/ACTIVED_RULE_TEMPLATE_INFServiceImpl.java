package com.yingfei.dataManagement.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.ACTIVED_RULE_TEMPLATE_INFMapper;
import com.yingfei.dataManagement.service.ACTIVED_RULE_TEMPLATE_INFService;
import com.yingfei.entity.domain.ACTIVED_RULE_TEMPLATE_INF;
import com.yingfei.entity.domain.DEF_DAT;
import com.yingfei.entity.domain.SPEC_INF;
import com.yingfei.entity.dto.ACTIVED_RULE_TEMPLATE_INF_DTO;
import com.yingfei.entity.vo.ACTIVED_RULE_TEMPLATE_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【ACTIVED_RULE_TEMPLATE_INF(储存报警规则模板信息表)】的数据库操作Service实现
 * @createDate 2024-05-08 16:26:42
 */
@Service
public class ACTIVED_RULE_TEMPLATE_INFServiceImpl extends ServiceImpl<ACTIVED_RULE_TEMPLATE_INFMapper, ACTIVED_RULE_TEMPLATE_INF>
        implements ACTIVED_RULE_TEMPLATE_INFService {

    @Override
    public long getTotal(ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        return baseMapper.getTotal(activedRuleTemplateInfVo);
    }

    @Override
    public List<ACTIVED_RULE_TEMPLATE_INF_DTO> getList(ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        return baseMapper.getList(activedRuleTemplateInfVo);
    }

    @Override
    public void add(ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        ACTIVED_RULE_TEMPLATE_INF activedRuleTemplateInf = new ACTIVED_RULE_TEMPLATE_INF();
        BeanUtils.copyPropertiesIgnoreNull(activedRuleTemplateInfVo, activedRuleTemplateInf);

        if (CollectionUtils.isNotEmpty(activedRuleTemplateInfVo.getChartOneList())){
            activedRuleTemplateInf.setF_CHART_ONE(JSONArray.toJSONString(activedRuleTemplateInfVo.getChartOneList()));
        }
        if (CollectionUtils.isNotEmpty(activedRuleTemplateInfVo.getChartTwoList())){
            activedRuleTemplateInf.setF_CHART_TWO(JSONArray.toJSONString(activedRuleTemplateInfVo.getChartTwoList()));
        }
        if (CollectionUtils.isNotEmpty(activedRuleTemplateInfVo.getChartThreeList())){
            activedRuleTemplateInf.setF_CHART_THREE(JSONArray.toJSONString(activedRuleTemplateInfVo.getChartThreeList()));
        }

        baseMapper.insert(activedRuleTemplateInf);
    }

    @Override
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<ACTIVED_RULE_TEMPLATE_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ACTIVED_RULE_TEMPLATE_INF::getF_ARTP, ids).set(ACTIVED_RULE_TEMPLATE_INF::getF_DEL, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void checkParam(ACTIVED_RULE_TEMPLATE_INF_VO activedRuleTemplateInfVo) {
        LambdaQueryWrapper<ACTIVED_RULE_TEMPLATE_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ACTIVED_RULE_TEMPLATE_INF::getF_NAME, activedRuleTemplateInfVo.getF_NAME()).eq(ACTIVED_RULE_TEMPLATE_INF::getF_DEL, DelFlagEnum.USE.getType());
        List<ACTIVED_RULE_TEMPLATE_INF> activedRuleTemplateInfList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(activedRuleTemplateInfList)) return;
        if (StringUtils.isNotEmpty(activedRuleTemplateInfVo.getF_ARTP())) {
            if (activedRuleTemplateInfList.size() > 1 || !Objects.equals(activedRuleTemplateInfList.get(0).getF_ARTP(), activedRuleTemplateInfVo.getF_ARTP())) {
                throw new BusinessException(DataManagementExceptionEnum.ACTIVED_RULE_TEMPLATE_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (activedRuleTemplateInfList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.ACTIVED_RULE_TEMPLATE_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }

    @Override
    public ACTIVED_RULE_TEMPLATE_INF_DTO info(Long id) {
        ACTIVED_RULE_TEMPLATE_INF activedRuleTemplateInf = baseMapper.selectById(id);
        ACTIVED_RULE_TEMPLATE_INF_DTO activedRuleTemplateInfDto = new ACTIVED_RULE_TEMPLATE_INF_DTO();
        if (activedRuleTemplateInf == null) return activedRuleTemplateInfDto;
        BeanUtils.copyPropertiesIgnoreNull(activedRuleTemplateInf, activedRuleTemplateInfDto);
        if (StringUtils.isNotEmpty(activedRuleTemplateInf.getF_CHART_ONE())){
            List<String> list = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_ONE(), String.class);
            activedRuleTemplateInfDto.setChartOneList(list);
        }
        if (StringUtils.isNotEmpty(activedRuleTemplateInf.getF_CHART_TWO())){
            List<String> list = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_TWO(), String.class);
            activedRuleTemplateInfDto.setChartTwoList(list);
        }
        if (StringUtils.isNotEmpty(activedRuleTemplateInf.getF_CHART_THREE())){
            List<String> list = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_THREE(), String.class);
            activedRuleTemplateInfDto.setChartThreeList(list);
        }
        return activedRuleTemplateInfDto;
    }
}





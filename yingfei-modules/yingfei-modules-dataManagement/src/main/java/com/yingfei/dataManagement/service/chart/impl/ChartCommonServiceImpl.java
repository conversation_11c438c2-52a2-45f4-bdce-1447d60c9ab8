package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.JsonEscapeUtil;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.mapper.SGRP_INFMapper;
import com.yingfei.dataManagement.mapper.convert.SubgroupDataConvert;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.ExportImgDTO;
import com.yingfei.entity.dto.chart.ExportImgWrapper;
import com.yingfei.entity.enums.PARAMETER_CHILDTypeEnum;
import com.yingfei.system.api.RemoteRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.aspectj.weaver.ast.Var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChartCommonServiceImpl implements ChartCommonService {

    @Resource
    private RedisService redisService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;
    @Resource
    private SGRP_VALService sgrpValService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private DEF_DATService defDatService;
    @Resource
    private SHIFT_DATService shiftDatService;
    @Resource
    private LOT_INFService lotInfService;
    @Resource
    private JOB_DATService jobDatService;
    @Resource
    private DESC_DATService descDatService;
    @Resource
    private SGRP_INFMapper sgrpInfMapper;
    @Resource
    private ANALYSIS_DASHBOARD_INFService analysisDashboardInfService;
    @Resource
    private RemoteRoleService remoteRoleService;

    /**
     * @param subgroupDataSelectionDTO
     * @param type                     1:单项分析 2:聚合分析
     * @return
     */
    @Override
    public Map<String, Object> getPageSubgroup(SubgroupDataSelectionDTO subgroupDataSelectionDTO, Integer type) {
        /*根据菜单获取分析模板配置*/
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(subgroupDataSelectionDTO.getMenuId());

        if (StringUtils.isEmpty(subgroupDataSelectionDTO.getParameterId())) {
            subgroupDataSelectionDTO.setParameterId(analysisDashboardInf.getF_PRST());
        }

        // 获取参数集ID
        PARAMETER_SET_INF_DTO parameterSetInfo = parameterSetInfService.info(subgroupDataSelectionDTO.getParameterId());

        // 获取参数集-工厂层级参数
        List<PARAMETER_CHILD_DTO> hierarchyParams = parameterSetInfo.getParameterChildDtoList().stream()
                .filter(param -> Objects.equals(param.getType(), PARAMETER_CHILDTypeEnum.HIERARCHY.getType()))
                .collect(Collectors.toList());

        // 验证参数存在性
        if (hierarchyParams.isEmpty() || hierarchyParams.get(0).getDataList() == null) {
            return Collections.emptyMap();
        }

        List<String> allowedHierarchies = hierarchyParams.get(0).getDataList();

        // 非管理员用户进行工厂层级校验
        EMPL_INF_DTO currentUser = SecurityUtils.getLoginUser().getSysUser();
        boolean isAdmin = remoteRoleService.isAdmin(currentUser.getF_ROLE()).getData();

        if (!isAdmin) {
            Long userHierarchyId = currentUser.getHierarchyInfDto().getF_HIER();
            if (!allowedHierarchies.contains(String.valueOf(userHierarchyId))) {
                return Collections.emptyMap();
            }
        }


        /*根据菜单获取分析模板配置*/
//        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(subgroupDataSelectionDTO.getMenuId());

        String conditionKey = String.format(RedisConstant.PAGE_ANALYZE_QUERY_CONDITION, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        String latestConditionKey = String.format(RedisConstant.PAGE_ANALYZE_LATEST_QUERY_CONDITION, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        String subgroupDataKey = String.format(RedisConstant.PAGE_ANALYZE_SUBGROUP_DATA, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        String latestSubgroupDataKey = String.format(RedisConstant.PAGE_ANALYZE_LATEST_SUBGROUP_DATA, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());

        redisService.deleteObject(conditionKey);
        redisService.deleteObject(latestConditionKey);
        redisService.deleteObject(subgroupDataKey);
        redisService.deleteObject(latestSubgroupDataKey);


        Map<String, Object> map = new HashMap<>();

        if (type == 1) {
            subgroupDataSelectionDTO = getData(subgroupDataSelectionDTO,parameterSetInfo);
        } else {
            subgroupDataSelectionDTO = getDataSelection(subgroupDataSelectionDTO, 1,parameterSetInfo);
        }
        if (subgroupDataSelectionDTO == null) return map;

        /*判断缓存是否存在*/
        if (redisService.getCacheObject(subgroupDataKey) != null) {
            map = getDynamicCondition(redisService.getCacheObject(subgroupDataKey), subgroupDataSelectionDTO);
            return map;
        }

        List<SubgroupDataDTO> subgroupDataDTOList = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return map;

        /*将条件和查询出的子组存入redis*/
        redisService.setCacheObject(conditionKey, subgroupDataSelectionDTO, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
        redisService.setCacheObject(subgroupDataKey, subgroupDataDTOList, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);

        /*用最新的一条作为查询条件存入redis*/
        getLatest(subgroupDataSelectionDTO);
        subgroupDataDTOList = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        redisService.setCacheObject(latestConditionKey, subgroupDataSelectionDTO, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
        redisService.setCacheObject(latestSubgroupDataKey, subgroupDataDTOList, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
        map = getDynamicCondition(subgroupDataDTOList, subgroupDataSelectionDTO);
        return map;
    }

    private Map<String, Object> getDynamicCondition(List<SubgroupDataDTO> subgroupDataDTOList, SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        Map<String, Object> map = new HashMap<>();
        /*获取工作动态条件*/
        Set<Long> jobList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_JOB).filter(fJob -> fJob != null && fJob != 0L).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(jobList)) {
            List<JOB_DAT> jobDatList = jobDatService.listByIds(jobList);
            map.put(Constants.jobList, jobDatList);
        }

        /*获取班次动态条件*/
        Set<Long> shiftList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SHIFT).filter(fShift -> fShift != null && fShift != 0L).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(shiftList)) {
            List<SHIFT_DAT> shiftDatList = shiftDatService.listByIds(shiftList);
            map.put(Constants.shiftList, shiftDatList);
        }

        /*获取批次动态条件*/
        Set<Long> lotList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_LOT).filter(flot -> flot != null && flot != 0L).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(lotList)) {
            List<LOT_INF> lotInfList = lotInfService.listByIds(lotList);
            map.put(Constants.lotList, lotInfList);
        }

        /*获取描述符动态条件*/
        List<SGRP_DSC> sgrpDscList = sgrpInfMapper.getSgrpDscList(subgroupDataSelectionDTO);
        Set<Long> dscList = sgrpDscList.stream().map(SGRP_DSC::getF_DESC).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(dscList)) {
            List<DESC_DAT_DTO> descDatList = descDatService.getSearchCondition(YesOrNoEnum.YES.getType(), new ArrayList<>(dscList));
            map.put(Constants.descList, descDatList);
        }
        return map;
    }

    @Override
    public SubgroupDataSelectionDTO getCacheCondition(SubgroupDataSelectionDTO subgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {
        String conditionKey = String.format(RedisConstant.PAGE_ANALYZE_QUERY_CONDITION, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        String latestConditionKey = String.format(RedisConstant.PAGE_ANALYZE_LATEST_QUERY_CONDITION, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        Integer offset = subgroupDataSelectionDTO.getOffset();
        Integer next = subgroupDataSelectionDTO.getNext();
        Date startTime = subgroupDataSelectionDTO.getStartTime();
        Date endTime = subgroupDataSelectionDTO.getEndTime();
        Integer fFlag = subgroupDataSelectionDTO.getF_FLAG();
        String identification = subgroupDataSelectionDTO.getIdentification();
        Integer evntStatus = subgroupDataSelectionDTO.getEvntStatus();
        Integer dbType = subgroupDataSelectionDTO.getDbType();
        Integer maxNum = subgroupDataSelectionDTO.getMaxNum();
        if (analysisChartConfigDTO.getIsTopOne() == YesOrNoEnum.NO.getType() && subgroupDataSelectionDTO.getIsRange() == YesOrNoEnum.NO.getType()) {
            SubgroupDataSelectionDTO cacheObject = redisService.getCacheObject(latestConditionKey);
            if (cacheObject == null) return subgroupDataSelectionDTO;
            if (subgroupDataSelectionDTO.getF_TEST() != null) {
                cacheObject.setF_TEST(subgroupDataSelectionDTO.getF_TEST());
            }
            if (subgroupDataSelectionDTO.getF_PART() != null) {
                cacheObject.setF_PART(subgroupDataSelectionDTO.getF_PART());
            }
            if (subgroupDataSelectionDTO.getF_PRCS() != null) {
                cacheObject.setF_PRCS(subgroupDataSelectionDTO.getF_PRCS());
            }
            if (subgroupDataSelectionDTO.getF_REV() != null) {
                cacheObject.setF_REV(subgroupDataSelectionDTO.getF_REV());
            }
            subgroupDataSelectionDTO = cacheObject;
        } else {
            if (CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getPartList()) ||
                    CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getPrcsList()) ||
                    CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getTestList())) {
                return subgroupDataSelectionDTO;
            }
            subgroupDataSelectionDTO = redisService.getCacheObject(conditionKey);
        }
        if (subgroupDataSelectionDTO == null) return subgroupDataSelectionDTO;
        subgroupDataSelectionDTO.setNext(next);
        subgroupDataSelectionDTO.setOffset(offset);
        subgroupDataSelectionDTO.setF_FLAG(fFlag);
        subgroupDataSelectionDTO.setIdentification(identification);
        subgroupDataSelectionDTO.setEvntStatus(evntStatus);
        subgroupDataSelectionDTO.setDbType(dbType);
        subgroupDataSelectionDTO.setMaxNum(maxNum);
        if (startTime != null) {
            subgroupDataSelectionDTO.setStartTime(startTime);
        }
        if (endTime != null) {
            subgroupDataSelectionDTO.setEndTime(endTime);
        }
        return subgroupDataSelectionDTO;
    }

    @Override
    public List<SubgroupDataDTO> getCacheSubgroupData(SubgroupDataSelectionDTO subgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {
        String subgroupDataKey = String.format(RedisConstant.PAGE_ANALYZE_SUBGROUP_DATA, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        String latestSubgroupDataKey = String.format(RedisConstant.PAGE_ANALYZE_LATEST_SUBGROUP_DATA, subgroupDataSelectionDTO.getParameterId() + subgroupDataSelectionDTO.getIdentification());
        List<SubgroupDataDTO> subgroupDataDTOList;
        if (analysisChartConfigDTO.getIsTopOne() == YesOrNoEnum.NO.getType() && subgroupDataSelectionDTO.getIsRange() == YesOrNoEnum.NO.getType()) {
            subgroupDataDTOList = redisService.getCacheObject(latestSubgroupDataKey);
        } else {
            subgroupDataDTOList = redisService.getCacheObject(subgroupDataKey);
        }
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return new ArrayList<>();
        return subgroupDataDTOList;
    }

    /**
     * 根据子组列表重新组装子组测试信息列表
     *
     * @return
     */
    @Override
    public List<SubgroupDataDTO> reassembly(List<SubgroupDataDTO> subgroupDataDTOList) {
        /*通过测试数据重新组装子组*/
        List<SubgroupDataDTO> subgroupDataDTOArrayList = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            subgroupDataDTO.getSgrpValDtoList().forEach(sgrpValDto -> {
                SubgroupDataDTO convert = SubgroupDataConvert.INSTANCE.convert(subgroupDataDTO);
                List<SGRP_VAL_DTO> list = new ArrayList<>();
                list.add(sgrpValDto);
                convert.setSgrpValDtoList(list);
                convert.setTestName(sgrpValDto.getTestName());
                convert.setTestType(sgrpValDto.getTestType());
                convert.setSgrpValDto(sgrpValDto);
                convert.setF_TEST(sgrpValDto.getF_TEST());
                subgroupDataDTOArrayList.add(convert);
            });
        });
        return subgroupDataDTOArrayList;
    }

    /**
     * 根据子组测试列表重新组装测试信息列表
     *
     * @return
     */
    @Override
    public List<SubgroupDataDTO> testValReassembly(List<SubgroupDataDTO> subgroupDataDTOList) {
        /*通过测试数据重新组装子组*/
        List<SubgroupDataDTO> subgroupDataDTOArrayList = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getTestList().forEach(test -> {
                SubgroupDataDTO convert = SubgroupDataConvert.INSTANCE.convert(subgroupDataDTO);
                if (StringUtils.isNotEmpty(test.getDefectId())) {
                    DEF_DAT defDat = defDatService.getById(test.getDefectId());
                    if (defDat != null) test.setDefectName(defDat.getF_NAME());
                }
                convert.setTestValDto(test);
                subgroupDataDTOArrayList.add(convert);
            });
        });
        return subgroupDataDTOArrayList;
    }

    static int maxSize = 50;
    static int charactertNum = 11;

    @Override
    public void exportExcel(String menuId, ExportImgWrapper exportImgWrapper, HttpServletResponse response) {
        try {
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("Images");
            List<ExportImgDTO> exportImgDTOList = exportImgWrapper.getExportImgDTOList();
            Map<String, String> titleMap = exportImgWrapper.getMap();
            if (MapUtils.isEmpty(titleMap)) {
                titleMap = new HashMap<>();
                titleMap.put("参数集", "参数集");
                titleMap.put("日期范围", "");
                titleMap.put("导出日期", DateUtils.dateTimeTwo(DateUtils.getNowDate()));
                titleMap.put("导出用户", "admin");
            }
            int rowIndex = 0;

            /*获取最大列数*/
            int size = exportImgDTOList.stream().map(ExportImgDTO::getTitleList).filter(CollectionUtils::isNotEmpty).collect(Collectors.toList())
                    .stream().map(List::size)
                    .max(Integer::compareTo)
                    .orElse(0);
            if (size > maxSize) maxSize = size;

            /*固定列宽度: 占11个汉字*/
            int width = charactertNum * 2 * 256;

            /*通用白底*/
            XSSFCellStyle whiteCellStyle = workbook.createCellStyle();
            whiteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex()); // 设置背景为白色
            whiteCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);


            /*设置标题*/
            String titleName = exportImgWrapper.getTitleName();
            if (StringUtils.isEmpty(titleName)) titleName = "分析报告";
            XSSFRow titleNameRow = sheet.createRow(rowIndex);
            titleNameRow.setHeightInPoints(27f);
            CellStyle titleNameCellStyle = workbook.createCellStyle();
            XSSFCell titleNameCell = titleNameRow.createCell(0);
            titleNameCell.setCellValue(titleName);
            /*设置加粗*/
            setFont(workbook, titleNameCell, titleNameCellStyle, 18);
            /*设置白底*/
            setWhite(workbook, titleNameCell, titleNameCellStyle);
            /*合并居中*/
            setCenter(workbook, titleNameCell, titleNameCellStyle);
            if (size > 0) {
                sheet.addMergedRegion(new CellRangeAddress(
                        rowIndex, // 起始行索引
                        rowIndex, // 结束行索引
                        0, // 起始列索引
                        size  // 结束列索引
                ));
            }
            if (size < maxSize) {
                for (int i = size; i < maxSize + 1; i++) {
                    XSSFCell cell = titleNameRow.createCell(i);
                    cell.setCellValue("");
                    cell.setCellStyle(whiteCellStyle);
                }
            }

            rowIndex++;


            CellStyle titleMapCellStyle = workbook.createCellStyle();
            for (String s : titleMap.keySet()) {
                XSSFRow row = sheet.createRow(rowIndex);
                XSSFCell cellOne = row.createCell(0);
                sheet.setColumnWidth(0, width);
                row.setHeightInPoints(19.5f);
                cellOne.setCellValue(s);
                setFont(workbook, cellOne, titleMapCellStyle, null);
                setWhite(workbook, cellOne, titleMapCellStyle);

                XSSFCell cellTwo = row.createCell(1);
                sheet.setColumnWidth(1, width);
                cellTwo.setCellValue(titleMap.get(s));
                cellTwo.setCellStyle(whiteCellStyle);

                for (int i = 2; i < maxSize + 1; i++) {
                    XSSFCell cell = row.createCell(i);
                    cell.setCellValue("");
                    sheet.setColumnWidth(i, width);
                    cell.setCellStyle(whiteCellStyle);
                }
                rowIndex++;
            }

            /*添加白底空白行*/
//            rowIndex++;
//            addWhiteLine(rowIndex, sheet, workbook, whiteCellStyle);
//            rowIndex++;


            for (ExportImgDTO exportImgDTO : exportImgDTOList) {
                XSSFCellStyle chartNameCellStyle = workbook.createCellStyle();
                if (StringUtils.isNotEmpty(exportImgDTO.getChartName())) {
                    XSSFRow chartNameRow = sheet.createRow(rowIndex);
                    chartNameRow.setHeightInPoints(24.5f);
                    XSSFCell chartNameCell = chartNameRow.createCell(0);
                    chartNameCell.setCellValue(exportImgDTO.getChartName());
                    setFont(workbook, chartNameCell, chartNameCellStyle, 16);
                    setCenter(workbook, chartNameCell, chartNameCellStyle);
                    sheet.addMergedRegion(new CellRangeAddress(
                            rowIndex, // 起始行索引
                            rowIndex, // 结束行索引
                            0, // 起始列索引
                            size  // 结束列索引
                    ));
                    if (size < maxSize) {
                        for (int i = size; i < maxSize + 1; i++) {
                            XSSFCell cell = chartNameRow.createCell(i);
                            cell.setCellValue("");
                            cell.setCellStyle(whiteCellStyle);
                        }
                    }

                    rowIndex++;
                }

                // 插入标题
                XSSFCellStyle titleCellStyle = workbook.createCellStyle();
                if (!CollectionUtils.isEmpty(exportImgDTO.getTitleList())) {
                    /*插入数据明细栏*/
                    XSSFCellStyle dataCellStyle = workbook.createCellStyle();
                    XSSFRow dataRow = sheet.createRow(rowIndex);
                    dataRow.setHeightInPoints(19.5f);
                    XSSFCell dataCell = dataRow.createCell(0);
                    dataCell.setCellValue("数据明细");
                    setFont(workbook, dataCell, dataCellStyle, 14);
                    setWhite(workbook, dataCell, dataCellStyle);
                    if (size < maxSize) {
                        for (int i = 1; i < maxSize + 1; i++) {
                            XSSFCell cell = dataRow.createCell(i);
                            cell.setCellValue("");
                            cell.setCellStyle(whiteCellStyle);
                        }
                    }

                    rowIndex++;
                    XSSFRow titleRow = sheet.createRow(rowIndex);
                    titleRow.setHeightInPoints(19.5f);
                    List<String> titleList = exportImgDTO.getTitleList();
                    for (int i = 0; i < titleList.size(); i++) {
                        XSSFCell cell = titleRow.createCell(i);
                        cell.setCellValue(titleList.get(i));
                        setFont(workbook, cell, titleCellStyle, null);
                        setBorder(workbook, cell, titleCellStyle);
                    }

                    if (titleList.size() < maxSize) {
                        for (int i = titleList.size(); i < maxSize + 1; i++) {
                            XSSFCell valueCell = titleRow.createCell(i);
                            valueCell.setCellValue("");
                            valueCell.setCellStyle(whiteCellStyle);
                        }
                    }
                    rowIndex++;
                }

                // 插入内容列表
                XSSFCellStyle contentCellStyle = workbook.createCellStyle();
                if (!CollectionUtils.isEmpty(exportImgDTO.getContentList())) {
                    List<List<Object>> contentList = exportImgDTO.getContentList();
                    for (List<Object> contentRowList : contentList) {
                        XSSFRow contentRow = sheet.createRow(rowIndex);
                        contentRow.setHeightInPoints(19.5f);
                        for (int j = 0; j < contentRowList.size(); j++) {
                            XSSFCell cell = contentRow.createCell(j);
                            if(ObjectUtils.isNotEmpty(contentRowList.get(j))){
                                final Object cellData = contentRowList.get(j);
                                if(cellData instanceof String){
                                    String cellDataStr = (String) cellData;
                                    if(JsonEscapeUtil.containsSpecialCharacters(cellDataStr)){
                                        cellDataStr = JsonEscapeUtil.unescapeHtml(cellDataStr);
                                        cell.setCellValue(cellDataStr);
                                    }
                                }else{
                                    cell.setCellValue(contentRowList.get(j)+"");
                                }
                            }else{
                                cell.setCellValue("");
                            }

                            setBorder(workbook, cell, contentCellStyle);
                        }

                        if (contentRowList.size() < maxSize) {
                            for (int i = contentRowList.size(); i < maxSize + 1; i++) {
                                XSSFCell valueCell = contentRow.createCell(i);
                                valueCell.setCellValue("");
                                valueCell.setCellStyle(whiteCellStyle);
                            }
                        }
                        rowIndex++;
                    }


                    /*添加白底空白行*/
                    addWhiteLine(rowIndex, sheet, workbook, whiteCellStyle);
                    rowIndex++;
                }
                if (!CollectionUtils.isEmpty(exportImgDTO.getContentMapList())) {
                    /*插入数据明细栏*/
                    XSSFCellStyle dataCellStyle = workbook.createCellStyle();
                    XSSFRow dataRow = sheet.createRow(rowIndex);
                    dataRow.setHeightInPoints(19.5f);
                    XSSFCell dataCell = dataRow.createCell(0);
                    dataCell.setCellValue("数据明细");
                    setFont(workbook, dataCell, dataCellStyle, 14);
                    setWhite(workbook, dataCell, dataCellStyle);
                    if (size < maxSize) {
                        for (int i = 1; i < maxSize + 1; i++) {
                            XSSFCell cell = dataRow.createCell(i);
                            cell.setCellValue("");
                            cell.setCellStyle(whiteCellStyle);
                        }
                    }

                    rowIndex++;

                    List<List<Map<String, String>>> contentMapList = exportImgDTO.getContentMapList();
                    XSSFCellStyle keyCellStyle = workbook.createCellStyle();
                    XSSFCellStyle valueCellStyle = workbook.createCellStyle();
                    for (List<Map<String, String>> mapList : contentMapList) {
                        XSSFRow contentRow = sheet.createRow(rowIndex);
                        int cellIndex = 0;
                        for (Map<String, String> map : mapList) {
                            for (Map.Entry<String, String> entry : map.entrySet()) {
                                String key = entry.getKey();
                                String value = entry.getValue();

                                // 创建并写入单元格，分别为 key 和 value
                                XSSFCell keyCell = contentRow.createCell(cellIndex++);
                                keyCell.setCellValue(key);
                                setFont(workbook, keyCell, keyCellStyle, null);
                                setBorder(workbook, keyCell, keyCellStyle);

                                XSSFCell valueCell = contentRow.createCell(cellIndex++);
                                valueCell.setCellValue(value);
                                setBorder(workbook, valueCell, valueCellStyle);
                            }
                        }
                        if (cellIndex < 5) {
                            for (int i = cellIndex; i < 6; i++) {
                                XSSFCell valueCell = contentRow.createCell(i);
                                valueCell.setCellValue("");
                                setBorder(workbook, valueCell, valueCellStyle);
                                cellIndex++;
                            }
                            if (mapList.size() == 1) {
                                /*合并第一行*/
                                sheet.addMergedRegion(new CellRangeAddress(
                                        rowIndex, // 起始行索引
                                        rowIndex, // 结束行索引
                                        0, // 起始列索引
                                        5  // 结束列索引
                                ));
                            }
                        }
                        if (cellIndex < maxSize) {
                            for (int i = cellIndex; i < maxSize + 1; i++) {
                                XSSFCell valueCell = contentRow.createCell(i);
                                valueCell.setCellValue("");
                                valueCell.setCellStyle(whiteCellStyle);
                            }
                        }
                        rowIndex++;
                    }
                    /*添加白底空白行*/
                    addWhiteLine(rowIndex, sheet, workbook, whiteCellStyle);
                    rowIndex++;
                }


//                MultipartFile file = exportImgDTO.getFile();
                String imageBase64 = exportImgDTO.getImageBase64();
                if (imageBase64 != null) {
                    XSSFRow row = sheet.createRow(rowIndex);
                    XSSFDrawing drawingPatriarch = sheet.createDrawingPatriarch();

                    try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                        if (imageBase64.contains(",")) {
                            imageBase64 = imageBase64.substring(imageBase64.indexOf(",") + 1);
                        }
                        byte[] imageByte = Base64.getDecoder().decode(imageBase64);
                        InputStream inputStream = new ByteArrayInputStream(imageByte);
                        BufferedImage bufferedImage = ImageIO.read(inputStream);
                        ImageIO.write(bufferedImage, "png", byteArrayOutputStream);

                        int pictureIndex = workbook.addPicture(byteArrayOutputStream.toByteArray(), Workbook.PICTURE_TYPE_PNG);

                        // 设定图片的起始位置
                        int startCol = 0;
                        int startRow = rowIndex;

                        // 获取图片的宽高
                        int imgWidth = bufferedImage.getWidth();
                        int imgHeight = bufferedImage.getHeight();
                        row.setHeightInPoints(200);
                        /*合并行和列*/
                        sheet.addMergedRegion(new CellRangeAddress(
                                rowIndex, // 起始行索引
                                rowIndex, // 结束行索引
                                0, // 起始列索引
                                size  // 结束列索引
                        ));
                        if (size < maxSize) {
                            for (int i = size; i < maxSize + 1; i++) {
                                XSSFCell valueCell = row.createCell(i);
                                valueCell.setCellValue("");
                                valueCell.setCellStyle(whiteCellStyle);
                            }
                        }

                        // 获取每个像素在单元格中的高度
                        float cellHeightInPixels = sheet.getDefaultRowHeightInPoints() / 72f * 96f;  // 默认行高转换为像素（点到像素）
                        // 获取每个像素在单元格中的宽度
                        int colWidth = 80;

                        // 计算图片的缩放比例

                        // 计算终止列和终止行
                        int endCol = 6;
                        /*列像素*/
                        int columnPixel = (endCol - startCol) * colWidth;
                        double columnScale = (double) columnPixel / imgWidth;

                        /*计算缩放*/
                        int i = (int) ((imgHeight * columnScale) / cellHeightInPixels);
                        int endRow = startRow + i;


                        XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 1023,
                                255, (short) startCol, rowIndex, (short) 5, rowIndex + 1);
                        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);


                        XSSFPicture picture = drawingPatriarch.createPicture(anchor, pictureIndex);
//                        picture.resize();
                        rowIndex += 1;  // 调整下一个图片起始的行
                        addWhiteLine(rowIndex, sheet, workbook, whiteCellStyle);
                        rowIndex++;
                        inputStream.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

            }

            // 设置响应头以下载文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=images.xlsx");

            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("<---------------导出完成--------------->");
    }

    /**
     * 添加白底空白行
     *
     * @param rowIndex
     * @param sheet
     * @param workbook
     */
    private void addWhiteLine(int rowIndex, XSSFSheet sheet, XSSFWorkbook workbook, CellStyle whiteCellStyle) {
        XSSFRow row = sheet.createRow(rowIndex);
        for (int i = 0; i < maxSize; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellValue("");
            cell.setCellStyle(whiteCellStyle);
        }
    }

    /**
     * 设置边框样式
     */
    private static void setBorder(XSSFWorkbook workbook, XSSFCell cell, CellStyle cellStyle) {
        // 创建样式：设置边框为细线
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        cell.setCellStyle(cellStyle);
    }


    /**
     * 设置加粗和字体大小
     *
     * @param workbook
     * @param cell
     */
    private static void setFont(XSSFWorkbook workbook, XSSFCell cell, CellStyle cellStyle, Integer size) {
        // 创建字体并设置为加粗
        Font boldFont = workbook.createFont();
        boldFont.setBold(true); // 设置字体加粗
        if (size != null) {
            boldFont.setFontHeightInPoints(Short.parseShort(String.valueOf(size)));
        }
        // 创建单元格样式
        cellStyle.setFont(boldFont);
        // 应用样式到单元格
        cell.setCellStyle(cellStyle);
    }

    /**
     * 设置白底
     *
     * @param workbook
     * @param cell
     */
    private static void setWhite(XSSFWorkbook workbook, XSSFCell cell, CellStyle cellStyle) {
        cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex()); // 设置背景为白色
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 应用样式到单元格
        cell.setCellStyle(cellStyle);
    }

    /**
     * 设置居中
     *
     * @param workbook
     * @param cell
     */
    private static void setCenter(XSSFWorkbook workbook, XSSFCell cell, CellStyle cellStyle) {
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        // 应用样式到单元格
        cell.setCellStyle(cellStyle);
    }

    /**
     * 获取最新一条当查询条件
     *
     * @param subgroupDataSelectionDTO
     * @return
     */
    private SubgroupDataSelectionDTO getLatest(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        /*控制图和直方图的测试条件只有一个*/
        SubgroupDataSelectionDTO sgrp_ext = new SubgroupDataSelectionDTO();

        if (subgroupDataSelectionDTO.getF_TEST() != null) {
            sgrp_ext.setF_TEST(subgroupDataSelectionDTO.getF_TEST());
        } else {
            sgrp_ext.setTestList(subgroupDataSelectionDTO.getTestList());
        }
        if (subgroupDataSelectionDTO.getF_PART() != null) {
            sgrp_ext.setF_PART(subgroupDataSelectionDTO.getF_PART());
        } else {
            sgrp_ext.setPartList(subgroupDataSelectionDTO.getPartList());
        }
        if (subgroupDataSelectionDTO.getF_PRCS() != null) {
            sgrp_ext.setF_PRCS(subgroupDataSelectionDTO.getF_PRCS());
        } else {
            sgrp_ext.setPrcsList(subgroupDataSelectionDTO.getPrcsList());
        }
        if (subgroupDataSelectionDTO.getF_REV() != null) {
            sgrp_ext.setF_REV(subgroupDataSelectionDTO.getF_REV());
        } else {
            sgrp_ext.setPartList(subgroupDataSelectionDTO.getPartList());
        }
        sgrp_ext.setF_FLAG(subgroupDataSelectionDTO.getF_FLAG());
        sgrp_ext.setDbType(subgroupDataSelectionDTO.getDbType());
        /*过程和产品拿查询出来的数据中最新的过程和产品*/
        SubgroupDataDTO topOne = sgrpInfService.getTopOne(sgrp_ext);
        if (topOne != null) {
            if (subgroupDataSelectionDTO.getF_PART() == null) {
                subgroupDataSelectionDTO.setF_PART(topOne.getF_PART());
                subgroupDataSelectionDTO.setPartList(null);
            }
            if (subgroupDataSelectionDTO.getF_REV() == null) {
                subgroupDataSelectionDTO.setF_REV(topOne.getF_REV());
                subgroupDataSelectionDTO.setPtrvList(null);
            }
            if (subgroupDataSelectionDTO.getF_PRCS() == null) {
                subgroupDataSelectionDTO.setF_PRCS(topOne.getF_PRCS());
                subgroupDataSelectionDTO.setPrcsList(null);
            }
            if (subgroupDataSelectionDTO.getF_TEST() == null) {
                LambdaQueryWrapper<SGRP_VAL> queryWrapper = new LambdaQueryWrapper<>();
                if (!CollectionUtils.isEmpty(sgrp_ext.getTestList())) {
                    queryWrapper.in(SGRP_VAL::getF_TEST, sgrp_ext.getTestList());
                }
                queryWrapper.eq(SGRP_VAL::getF_SGRP, topOne.getF_SGRP()).orderByDesc(SGRP_VAL::getF_SGTM);
                List<SGRP_VAL> sgrpValList = sgrpValService.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(sgrpValList)) {
                    subgroupDataSelectionDTO.setF_TEST(sgrpValList.get(0).getF_TEST());
                    subgroupDataSelectionDTO.setTestList(null);
                }
            }
        }
        return subgroupDataSelectionDTO;
    }

    /**
     * 获取聚合分析自定义查询条件
     *
     * @param subgroupDataSelectionDTO 自定义查询条件
     * @param type                     0:异常数据 1:控制图和直方图和查看数据等
     * @return
     */
    public SubgroupDataSelectionDTO getDataSelection(SubgroupDataSelectionDTO subgroupDataSelectionDTO, Integer type,PARAMETER_SET_INF_DTO parameterSetInfDto) {
        if (ObjectUtils.isNotEmpty(parameterSetInfDto)) {
            JudgeUtils.isNull(parameterSetInfDto, DataManagementExceptionEnum.PARAMETER_SET_NOT_EXISTS);

            Map<String, List<Long>> map = parameterSetInfService.getSearchCondition(parameterSetInfDto.getParameterChildDtoList());
            parameterSetInfDto.setMap(map);
            List<Long> F_TESTList = map.get(Constants.testList) == null ? new ArrayList<>() :
                    map.get(Constants.testList);
            List<Long> F_PARTList = map.get(Constants.partList) == null ? new ArrayList<>() :
                    map.get(Constants.partList);
            List<Long> F_PRCSList = map.get(Constants.prcsList) == null ? new ArrayList<>() :
                    map.get(Constants.prcsList);
            if (type == 1) {
                extracted(subgroupDataSelectionDTO, F_TESTList, F_PARTList, F_PRCSList);
            } else {
                if (Objects.isNull(subgroupDataSelectionDTO.getF_PART())) {
                    if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList()))
                        subgroupDataSelectionDTO.setPartList(F_PARTList);
                }
                if (Objects.isNull(subgroupDataSelectionDTO.getF_PRCS())) {
                    if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList()))
                        subgroupDataSelectionDTO.setPrcsList(F_PRCSList);
                }
                if (Objects.isNull(subgroupDataSelectionDTO.getF_TEST())) {
                    if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList()))
                        subgroupDataSelectionDTO.setTestList(F_TESTList);
                }
            }

            subgroupDataSelectionDTO = SubgroupDataSelectionDTO.getData(subgroupDataSelectionDTO, parameterSetInfDto);
        }

        else {
            if (type == 1 && CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getTestList()) && CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getPartList()) && CollectionUtils.isNotEmpty(subgroupDataSelectionDTO.getPrcsList())) {
                extracted(subgroupDataSelectionDTO, subgroupDataSelectionDTO.getTestList(), subgroupDataSelectionDTO.getPartList(), subgroupDataSelectionDTO.getPrcsList());
            }
        }
        return subgroupDataSelectionDTO;
    }

    /**
     * 获取单项分析自定义查询条件
     *
     * @param subgroupDataSelectionDTO
     * @return
     */
    public SubgroupDataSelectionDTO getData(SubgroupDataSelectionDTO subgroupDataSelectionDTO,PARAMETER_SET_INF_DTO parameterSetInfDto) {
        List<Long> partList;
        List<Long> pacsList;
        List<Long> testList;
        if (parameterSetInfDto == null) {
            parameterSetInfDto = new PARAMETER_SET_INF_DTO();
            if (subgroupDataSelectionDTO.getF_PART() == null || subgroupDataSelectionDTO.getF_PRCS() == null || subgroupDataSelectionDTO.getF_TEST() == null) {
                return null;
            }
            partList = Collections.singletonList(subgroupDataSelectionDTO.getF_PART());
            pacsList = Collections.singletonList(subgroupDataSelectionDTO.getF_PRCS());
            testList = Collections.singletonList(subgroupDataSelectionDTO.getF_TEST());
        } else {
            Map<String, List<Long>> map = parameterSetInfService.getSearchCondition(parameterSetInfDto.getParameterChildDtoList());
            parameterSetInfDto.setMap(map);
            testList = map.get(Constants.testList) == null ? new ArrayList<>() :
                    map.get(Constants.testList);
            partList = map.get(Constants.partList) == null ? new ArrayList<>() :
                    map.get(Constants.partList);
            pacsList = map.get(Constants.prcsList) == null ? new ArrayList<>() :
                    map.get(Constants.prcsList);
        }

        if (Objects.isNull(subgroupDataSelectionDTO.getF_PART())) {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList()))
                subgroupDataSelectionDTO.setPartList(partList);
        }
        if (Objects.isNull(subgroupDataSelectionDTO.getF_PRCS())) {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList()))
                subgroupDataSelectionDTO.setPrcsList(pacsList);
        }
        if (Objects.isNull(subgroupDataSelectionDTO.getF_TEST())) {
            if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList()))
                subgroupDataSelectionDTO.setTestList(testList);
        }
        /*先查询所有数据*/
        subgroupDataSelectionDTO = SubgroupDataSelectionDTO.getData(subgroupDataSelectionDTO, parameterSetInfDto);
//        subgroupDataSelectionDTO.setTotalNum(parameterSetInfDto.getF_MAX_ITEM() == null ? 1000 : parameterSetInfDto.getF_MAX_ITEM());
        log.info("聚合分析子组查询条件-------->" + JSON.toJSONString(subgroupDataSelectionDTO));
        /*箱线图,能力仪表盘只查询测试类型为变量的*/
//        subgroupDataSelectionDTO.setTestType(YesOrNoEnum.YES.getType());
        return subgroupDataSelectionDTO;
    }

    private void extracted(SubgroupDataSelectionDTO subgroupDataSelectionDTO, List<Long> F_TESTList, List<Long> F_PARTList, List<Long> F_PRCSList) {

        if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList())) {
            subgroupDataSelectionDTO.setTestList(F_TESTList);
        }
        if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList())) {
            subgroupDataSelectionDTO.setPartList(F_PARTList);
        }
        if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList())) {
            subgroupDataSelectionDTO.setPrcsList(F_PRCSList);
        }

    }

    /**
     * 生成三个列表元素的组合数据
     *
     * @param fpartList fpart 列表
     * @param fprcsList fprcs 列表
     * @param ftestList ftest 列表
     * @return 组合数据列表
     */
    @Override
    public List<Map<String, Long>> generateCombinations(List<Long> fpartList, List<Long> ptrvList, List<Long> fprcsList, List<Long> ftestList) {
        List<Map<String, Long>> result = new ArrayList<>();
        // 遍历 fpartList
        for (Long fpart : fpartList) {
            // 遍历 ptrvList
            for (Long fptrv : ptrvList) {
                // 遍历 fprcsList
                for (Long fprcs : fprcsList) {
                    // 遍历 ftestList
                    for (Long ftest : ftestList) {
                        Map<String, Long> combination = new HashMap<>();
                        combination.put(Constants.part, fpart);
                        combination.put(Constants.ptrv, fptrv);
                        combination.put(Constants.prcs, fprcs);
                        combination.put(Constants.test, ftest);
                        result.add(combination);
                    }
                }
            }
        }
        return result;
    }

    /**
     *  根据配置获取最大返回子组数的子组数据
     * @param subgroupDataDTOList 子组列表
     * @param maxNum 最大返回子组数
     * @param sort 1:正序 2:倒序
     */
    @Override
    public List<SubgroupDataDTO> getTotalNumSubgroup(List<SubgroupDataDTO> subgroupDataDTOList, Integer maxNum, int sort) {
        if(ObjectUtils.isNotEmpty(maxNum)){
            if (sort == YesOrNoEnum.YES.getType()) {
                /*根据subgroupDataDTOList 的F_SGTM字段正序排列*/
                subgroupDataDTOList = subgroupDataDTOList.stream()
                        .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP))
                        .limit(maxNum)
                        .collect(Collectors.toList());
            } else {
                subgroupDataDTOList = subgroupDataDTOList.stream()
                        .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP).reversed())
                        .limit(maxNum)
                        .collect(Collectors.toList());
            }
        }
        return subgroupDataDTOList;
    }
}

package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.CapabilityMatrixPotentialExpectDTO;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.CapabilityMatrixConfigDTO;
import com.yingfei.entity.dto.chart.InstrumentBoardDTO;

import java.util.List;

/**
 * 聚合分析
 */
public interface InstrumentBoardService {
    /**
     * 聚合分析详情
     */
    List<InstrumentBoardDTO> clusterAnalysisInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    /**
     * 能力矩阵计算
     */
    CapabilityMatrixPotentialExpectDTO competencyMatrix(DataSummaryDTO dataSummaryDTO, CapabilityMatrixConfigDTO capabilityMatrixConfigDTO);

    /**
     * 获取能力趋势
     *
     * @param subgroupDataSelectionDTO 能力趋势
     * @return
     */
    List<STREAM_TREND_INF_DTO> getTrend(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    List<List<STREAM_TREND_INF_DTO>> getTrendList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);
}

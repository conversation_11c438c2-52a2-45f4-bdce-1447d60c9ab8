package com.yingfei.dataManagement.service.gauge.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.service.gauge.GAUGE_DEVICEService;
import com.yingfei.dataManagement.service.gauge.GAUGE_FORMATService;
import com.yingfei.entity.domain.GAUGE_DEVICE;
import com.yingfei.entity.domain.GAUGE_FORMAT;
import com.yingfei.dataManagement.mapper.GAUGE_FORMATMapper;
import com.yingfei.entity.dto.GAUGE_FORMAT_ADVANCED_DTO;
import com.yingfei.entity.dto.GAUGE_FORMAT_CONFIG_DTO;
import com.yingfei.entity.dto.GAUGE_FORMAT_DTO;
import com.yingfei.entity.vo.GAUGE_FORMAT_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description 针对表【GAUGE_FORMAT(量具解析规则配置表)】的数据库操作Service实现
 * @createDate 2024-07-30 11:19:16
 */
@Service
public class GAUGE_FORMATServiceImpl extends ServiceImpl<GAUGE_FORMATMapper, GAUGE_FORMAT>
        implements GAUGE_FORMATService {

    @Resource
    private GAUGE_DEVICEService gaugeDeviceService;

    @Override
    public long getTotal(GAUGE_FORMAT_VO gaugeFormatVo) {
        return 0;
    }

    @Override
    public List<GAUGE_FORMAT_DTO> getList(GAUGE_FORMAT_VO gaugeFormatVo) {
        List<GAUGE_FORMAT_DTO> gaugeFormatDtoList = baseMapper.getList(gaugeFormatVo);
        return gaugeFormatDtoList;
    }

    @Override
    public void add(GAUGE_FORMAT_VO gaugeFormatVo) {
        GAUGE_FORMAT gaugeFormat = new GAUGE_FORMAT();
        BeanUtils.copyPropertiesIgnoreNull(gaugeFormatVo, gaugeFormat);
        String jsonString = JSONArray.toJSONString(gaugeFormatVo.getGaugeFormatConfigDtoList());
        gaugeFormat.setF_DATA_CONFIG(jsonString);
        String advancedDto = JSONObject.toJSONString(gaugeFormatVo.getGaugeFormatAdvancedDto());
        gaugeFormat.setF_ADVANCED(advancedDto);
        baseMapper.insert(gaugeFormat);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
        /*删除对应的量具设备*/
        LambdaQueryWrapper<GAUGE_DEVICE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GAUGE_DEVICE::getF_GAFO,ids);
        gaugeDeviceService.remove(queryWrapper);
    }

    @Override
    public void checkParam(GAUGE_FORMAT_VO gaugeFormatVo) {
        if (StringUtils.isEmpty(gaugeFormatVo.getF_NAME())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        LambdaQueryWrapper<GAUGE_FORMAT> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GAUGE_FORMAT::getF_NAME, gaugeFormatVo.getF_NAME());
        List<GAUGE_FORMAT> gaugeFormatList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(gaugeFormatList)) return;
        if (StringUtils.isNotEmpty(gaugeFormatVo.getF_GAFO())) {
            if (gaugeFormatList.size() > 1 || !Objects.equals(gaugeFormatList.get(0).getF_GAFO(), gaugeFormatVo.getF_GAFO())) {
                throw new BusinessException(DataManagementExceptionEnum.GAUGE_FORMAT_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (gaugeFormatList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.GAUGE_FORMAT_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }

    @Override
    public GAUGE_FORMAT_DTO info(Long id) {
        GAUGE_FORMAT gaugeFormat = baseMapper.selectById(id);
        GAUGE_FORMAT_DTO gaugeFormatDto = new GAUGE_FORMAT_DTO();
        BeanUtils.copyPropertiesIgnoreNull(gaugeFormat,gaugeFormatDto);
        if (StringUtils.isNotEmpty(gaugeFormatDto.getF_DATA_CONFIG())){
            List<GAUGE_FORMAT_CONFIG_DTO> gaugeFormatConfigDtoList =
                    JSONArray.parseArray(gaugeFormatDto.getF_DATA_CONFIG(), GAUGE_FORMAT_CONFIG_DTO.class);
            gaugeFormatDto.setGaugeFormatConfigDtoList(gaugeFormatConfigDtoList);
        }
        if (StringUtils.isNotEmpty(gaugeFormatDto.getF_ADVANCED())){
            GAUGE_FORMAT_ADVANCED_DTO gaugeFormatAdvancedDto = 
                    JSONObject.parseObject(gaugeFormatDto.getF_ADVANCED(), GAUGE_FORMAT_ADVANCED_DTO.class);
            gaugeFormatDto.setGaugeFormatAdvancedDto(gaugeFormatAdvancedDto);
        }
        return gaugeFormatDto;
    }
}





package com.yingfei.dataManagement.service.chart;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_INF_DTO;
import com.yingfei.entity.vo.ANALYSIS_DASHBOARD_INF_VO;

/**
 * @description 针对表【ANALYSIS_DASHBOARD_INF(分析页面关联表)】的数据库操作Service
 * @createDate 2024-09-04 16:01:11
 */
public interface ANALYSIS_DASHBOARD_INFService extends IService<ANALYSIS_DASHBOARD_INF>, BaseService<ANALYSIS_DASHBOARD_INF_VO, ANALYSIS_DASHBOARD_INF_DTO> {

    ANALYSIS_DASHBOARD_INF findByMenuId(Long menuId);

    void edit(ANALYSIS_DASHBOARD_INF_VO analysisDashboardInfVo);
}

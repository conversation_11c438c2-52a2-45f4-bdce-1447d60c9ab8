package com.yingfei.dataManagement.service.chart.impl;

import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.dataManagement.service.PARAMETER_SET_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_INFService;
import com.yingfei.dataManagement.service.chart.AggregateAnalysisCommonService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF;
import com.yingfei.entity.dto.PARAMETER_SET_INF_DTO;
import com.yingfei.entity.enums.TimeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

@Service
public class AggregateAnalysisCommonServiceImpl implements AggregateAnalysisCommonService {

    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;
    @Resource
    private ANALYSIS_DASHBOARD_INFService analysisDashboardInfService;


    @Override
    public Map<String, Object> getParameter(Long menuId) {
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(menuId);
        if (analysisDashboardInf == null)
            throw new BusinessException(DataManagementExceptionEnum.MENU_NOT_ASSOCIATED_PARAMETER_SET);
        PARAMETER_SET_INF_DTO parameterSetInfDto = parameterSetInfService.info(analysisDashboardInf.getF_PRST());
        Map<String, Object> map = parameterSetInfService.getSearchConditionObject(parameterSetInfDto.getParameterChildDtoList());
        setTime(parameterSetInfDto, map);
        return map;
    }

    @Override
    public Map<String, Object> findByParameterId(Long id) {
        PARAMETER_SET_INF_DTO parameterSetInfDto = parameterSetInfService.info(id);
        Map<String, Object> map = parameterSetInfService.getSearchConditionObject(parameterSetInfDto.getParameterChildDtoList());
        setTime(parameterSetInfDto, map);
        return map;
    }

    public void setTime(PARAMETER_SET_INF_DTO parameterSetInfDto, Map<String, Object> map) {
        if (parameterSetInfDto.getF_TIME_WINDOW_TYPE() > 0) {
            Date date = DateUtils.getNowDate();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            switch (TimeEnum.getType(parameterSetInfDto.getF_DATERANGE_TYPE())) {
                case MINUTE:
                    calendar.add(Calendar.MINUTE, -parameterSetInfDto.getF_RANGE_INTERVAL());
                    break;
                case HOUR:
                    calendar.add(Calendar.HOUR, -parameterSetInfDto.getF_RANGE_INTERVAL());
                    break;
                case DAY:
                    calendar.add(Calendar.DAY_OF_MONTH, -parameterSetInfDto.getF_RANGE_INTERVAL());
                    break;
                case WEEK:
                    calendar.add(Calendar.DAY_OF_WEEK, -parameterSetInfDto.getF_RANGE_INTERVAL());
                    break;
                case MONTH:
                    calendar.add(Calendar.MONTH, -parameterSetInfDto.getF_RANGE_INTERVAL());
                    break;
                case YEAR:
                    calendar.add(Calendar.YEAR, -parameterSetInfDto.getF_RANGE_INTERVAL());
            }
            map.put("startTime", DateUtils.dateTimeTwo(calendar.getTime()));
            map.put("endTime", DateUtils.dateTimeTwo(date));
        } else {
            map.put("startTime", DateUtils.dateTimeTwo(parameterSetInfDto.getF_START_DATE()));
            map.put("endTime", DateUtils.dateTimeTwo(parameterSetInfDto.getF_END_DATE()));
        }
    }
}

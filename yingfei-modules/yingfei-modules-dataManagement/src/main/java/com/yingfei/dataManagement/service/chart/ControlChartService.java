package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.ControlChartDTO;

import java.util.List;

public interface ControlChartService {
    ControlChartDTO getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    List<ControlChartDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    DataSummaryDTO distribution(SubgroupDataDTO subgroupDataDTO);
}

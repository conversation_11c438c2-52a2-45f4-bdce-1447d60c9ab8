package com.yingfei.dataManagement.service.gauge.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.GAUGE_AGENTMapper;
import com.yingfei.dataManagement.service.gauge.GAUGE_AGENTService;
import com.yingfei.entity.domain.GAUGE_AGENT;
import com.yingfei.entity.dto.GAUGE_AGENT_DTO;
import com.yingfei.entity.vo.GAUGE_AGENT_VO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 针对表【GAUGE_AGENT(Agent和硬件标识对照表)】的数据库操作Service实现
 * @createDate 2024-07-30 11:19:00
 */
@Service
public class GAUGE_AGENTServiceImpl extends ServiceImpl<GAUGE_AGENTMapper, GAUGE_AGENT>
        implements GAUGE_AGENTService {

    @Resource
    private RedisService redisService;

    @Override
    public long getTotal(GAUGE_AGENT_VO gaugeAgentVo) {
        return 0;
    }

    @Override
    public List<GAUGE_AGENT_DTO> getList(GAUGE_AGENT_VO gaugeAgentVo) {
        return baseMapper.getList(gaugeAgentVo);
    }

    @Override
    public void add(GAUGE_AGENT_VO gaugeAgentVo) {

    }

    @Override
    public void del(List<Long> ids) {

    }

    @Override
    public void checkParam(GAUGE_AGENT_VO gaugeAgentVo) {

    }
}





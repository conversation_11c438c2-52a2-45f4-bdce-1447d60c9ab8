package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.DataReportService;
import com.yingfei.dataManagement.service.chart.HistogramService;
import com.yingfei.entity.domain.DESC_DAT;
import com.yingfei.entity.domain.DESC_GRP;
import com.yingfei.entity.domain.EVNT_INF;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.DataReportConfigDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataReportServiceImpl implements DataReportService {

    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SGRP_DSCService sgrpDscService;
    @Resource
    private DESC_DATService descDatService;
    @Resource
    private DESC_GRPService descGrpService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private HistogramService histogramService;
    @Resource
    private LOT_INFService lotInfService;
    @Resource
    private JOB_DATService jobDatService;
    @Resource
    private SHIFT_DATService shiftDatService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private TEMP_TABLE_INFService tempTableInfService;
    @Resource
    private ChartCommonService chartCommonService;


    @Override
    public Map<String, Object> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.DATA_REPORT);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.QUADRANT_CHART_CONFIG_NOT_EXISTS);
        }

        DataReportConfigDTO dataReportConfigDTO = new DataReportConfigDTO();
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            dataReportConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), DataReportConfigDTO.class);
        } else {
            Object chartConfig = analysisChartConfigDTO.getChartConfig();
            dataReportConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(chartConfig), DataReportConfigDTO.class);
        }
        /**
         * 1.hideTestInMinimizedStatus为true时
         *   1)hideDescInMinimizedStatus  为true时 查SGRP_INF表
         *   2)hideDescInMinimizedStatus  为false时 查SGRP_INF 和 SGRP_DESC两张表
         * 2.hideTestInMinimizedStatus为false时
         *   1)查SGRP_INF和 SGRP_DESC和 SGRP_CMT和 SGRP_VAL 四张表
         */
        Map<String, Object> viewMap = new HashMap<>();
        /*获取缓存查询条件*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return viewMap;

        if (dataReportConfigDTO.getHideDisabledSubgroups()) subgroupDataSelectionDTO.setF_FLAG(2);

        /*动态字段*/
        Set<Object> set = new HashSet<>();
        Map<Long, DESC_GRP> dscGrpMap = new HashMap<>();
        Map<Long, DESC_DAT> dscDatMap = new HashMap<>();
        List<SubgroupDataDTO> subgroupDataDTOList = new ArrayList<>();
        if (dataReportConfigDTO.getHideTestInMinimizedStatus() && !dataReportConfigDTO.getSizeChartEnabled()) {
            viewMap.put(Constants.TOTAL, sgrpInfService.getSgrpInfTotal(subgroupDataSelectionDTO));
            subgroupDataDTOList = sgrpInfService.getSgrpInfList(subgroupDataSelectionDTO);
            if (!dataReportConfigDTO.getHideDescInMinimizedStatus()) {
                getViewDesc(set, dscGrpMap, dscDatMap, subgroupDataDTOList);
            }

        } else {
            viewMap.put(Constants.TOTAL, sgrpInfService.getViewDataTotal(subgroupDataSelectionDTO));
            subgroupDataDTOList = sgrpInfService.getViewDataSubgroupDataDTOList(subgroupDataSelectionDTO);
            if (!dataReportConfigDTO.getHideDescInMaximizedStatus()) {
                getViewDesc(set, dscGrpMap, dscDatMap, subgroupDataDTOList);
            }
        }

        structureSubgroupDataDTOList(subgroupDataDTOList);

        /*按测试拆分*/
        if ((dataReportConfigDTO.getUnfoldValueSortType() == 1 && dataReportConfigDTO.getSizeChartEnabled()) ||
                (dataReportConfigDTO.getFoldValueSortType() == 1 && !dataReportConfigDTO.getSizeChartEnabled())) {
            subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
            subgroupDataDTOList = chartCommonService.testValReassembly(subgroupDataDTOList);
        }

        viewMap.put(Constants.LIST, subgroupDataDTOList);
        viewMap.put(Constants.SET, set);
        return viewMap;
    }

    /**
     * 获取子组描述符
     *
     * @param set
     * @param dscGrpMap
     * @param dscDatMap
     * @param subgroupDataDTOList
     */
    private void getViewDesc(Set<Object> set, Map<Long, DESC_GRP> dscGrpMap, Map<Long, DESC_DAT> dscDatMap, List<SubgroupDataDTO> subgroupDataDTOList) {
        if (CollectionUtils.isNotEmpty(subgroupDataDTOList)) {
            List<SGRP_DSC_DTO> sgrpDscList =
                    sgrpDscService.findBySgrpList(subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SGRP).collect(Collectors.toList()));
            Map<Long, List<SGRP_DSC_DTO>> map = sgrpDscList.stream().collect(Collectors.groupingBy(SGRP_DSC_DTO::getF_SGRP));
            /*查子组对应的描述符*/
            subgroupDataDTOList.forEach(subgroupDataDTO -> {
                List<SGRP_DSC_DTO> sgrpDscDtoList = map.get(subgroupDataDTO.getF_SGRP());
                if (CollectionUtils.isNotEmpty(sgrpDscDtoList)) {
                    subgroupDataDTO.setSgrpDscList(sgrpDscDtoList);
                    Map<String, String> headerMap = new HashMap<>();
                    sgrpDscDtoList.forEach(b -> {
                        DESC_GRP descGrp;
                        if (dscGrpMap.get(b.getF_DSGP()) == null) {
                            descGrp = descGrpService.getById(b.getF_DSGP());
                            dscGrpMap.put(b.getF_DSGP(), descGrp);
                            if (descGrp != null)
                                set.add(descGrp.getF_NAME());
                        } else {
                            descGrp = dscGrpMap.get(b.getF_DSGP());
                        }
                        DESC_DAT descDat;
                        if (dscDatMap.get(b.getF_DESC()) == null) {
                            descDat = descDatService.getById(b.getF_DESC());
                            dscDatMap.put(b.getF_DESC(), descDat);
                        } else {
                            descDat = dscDatMap.get(b.getF_DESC());
                        }
                        if (descGrp != null)
                            headerMap.merge(descGrp.getF_NAME(), descDat.getF_NAME(), (a1, b1) -> a1 + Constants.COMMA + b1);
                    });
                    subgroupDataDTO.setHeaderMap(headerMap);
                }
            });
        }
    }


    public void structureSubgroupDataDTOList(List<SubgroupDataDTO> subgroupDataDTOList) {
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            if (CollectionUtils.isNotEmpty(subgroupDataDTO.getSgrpValDtoList())) {
                subgroupDataDTO.getSgrpValDtoList().forEach(sgrpValDto -> {
                    subgroupDataDTO.setF_TEST(sgrpValDto.getF_TEST());
                    DataSummaryDTO dataSummaryDTO = new DataSummaryDTO();
                    /*获取公差限数据*/
                    SPEC_INF_DTO specInfDto = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTO);
                    sgrpValDto.getSgrpValChildDto().setSpecInfDto(specInfDto);
                });
            }

            /*判断是否报警*/
            List<EVNT_INF> evntInfList = evntInfService.findBySgrp(subgroupDataDTO.getF_SGRP());

            subgroupDataDTO.setEvntInfList(evntInfList);
            /*查询班次*/
//            subgroupDataDTO.setShiftDatDto(shiftDatService.info(subgroupDataDTO.getF_SHIFT()));
//            /*查询批次*/
//            subgroupDataDTO.setLotInfDto(lotInfService.info(subgroupDataDTO.getF_LOT()));
//            /*查询工单*/
//            subgroupDataDTO.setJobDatDto(jobDatService.info(subgroupDataDTO.getF_JOB()));
        });
    }
}

package com.yingfei.dataManagement.service.chart.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.service.EVNT_INFService;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.CalendarService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.entity.domain.BPM_PROCESS_INSTANCE;
import com.yingfei.entity.dto.EVNT_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.CalendarDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.vo.EVNT_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CalendarServiceImpl implements CalendarService {
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private BpmProcessInstanceService bpmProcessInstanceService;

    @Override
    public CalendarDTO getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.CALENDAR);

        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
        CalendarDTO calendarDTO = new CalendarDTO();

        /*获取缓存查询条件*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null)
            return calendarDTO;

        /*获取缓存子组*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) return calendarDTO;
        List<Long> sgrpIdList = subgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SGRP).collect(Collectors.toList());
        EVNT_INF_VO evntInfVo = new EVNT_INF_VO();
        evntInfVo.setStartTime(subgroupDataSelectionDTO.getStartDate());
        evntInfVo.setEndTime(subgroupDataSelectionDTO.getEndDate());
        evntInfVo.setNext(Constants.NEXT);
        evntInfVo.setDbType(InitConfig.getDriverType());
        List<EVNT_INF_DTO> evntInfDtoList = evntInfService.getList(evntInfVo);
        evntInfDtoList = evntInfDtoList.stream().filter(s -> sgrpIdList.contains(s.getF_SGRP())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(evntInfDtoList)) return calendarDTO;
        Map<String, List<EVNT_INF_DTO>> collect = evntInfDtoList.stream().collect(Collectors.groupingBy(s -> DateUtils.dateTime(s.getF_SGTM()),
                LinkedHashMap::new, Collectors.toList()));
        List<Integer> arrayList = new ArrayList<>();
        Map<String,Integer> dataMap = new HashMap<>();
        collect.forEach((k, v) -> {
            arrayList.add(Integer.valueOf(k.split("-")[0]));
            List<SubgroupDataDTO> dataDTOList =
                    subgroupDataDTOList.stream().filter(s -> DateUtils.dateTime(s.getF_SGTM()).equals(k)).collect(Collectors.toList());
            dataMap.put(k, dataDTOList.size());
            v.forEach(evntInfDto -> {
                LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_EVNT, evntInfDto.getF_EVNT());
                List<BPM_PROCESS_INSTANCE> list = bpmProcessInstanceService.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    evntInfDto.setBpmProcessInstanceList(list);
                }
            });
        });
        Integer max = Collections.max(arrayList);
        Integer min = Collections.min(arrayList);

        if (!Objects.equals(max, min)) calendarDTO.setType(1);
//        Map<String, Integer> collectMap = new HashMap<>();
//        collect.keySet().forEach(key -> {
//            collectMap.put(key,collect.get(key).size());
//        });

        calendarDTO.setMap(collect);
        calendarDTO.setDataMap(dataMap);
        return calendarDTO;
    }
}

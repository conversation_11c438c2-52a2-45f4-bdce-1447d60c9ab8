package com.yingfei.dataManagement.service.gauge.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.convert.GAUGE_CONNECTIONConvert;
import com.yingfei.dataManagement.service.gauge.*;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.enums.GAUGE_FORMAT_RETURN_TYPEEnum;
import com.yingfei.entity.vo.GAUGE_AGENT_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class GAUGE_COMMONServiceImpl implements GAUGE_COMMONService {

    @Resource
    private GAUGE_AGENTService gaugeAgentService;
    @Resource
    private GAUGE_CONNECTIONService gaugeConnectionService;
    @Resource
    private GAUGE_INTERFACEService gaugeInterfaceService;
    @Resource
    private GAUGE_DEVICEService gaugeDeviceService;
    @Resource
    private GAUGE_FORMATService gaugeFormatService;
    @Resource
    private RedisService redisService;

    public static Cache<String, String> cache = CacheBuilder.newBuilder()
            //设置缓存初始大小，应该合理设置，后续会扩容
            .initialCapacity(10)
            //最大值
            .maximumSize(100)
            //并发数设置
            .concurrencyLevel(5)
            //缓存过期时间，写入后20秒钟过期
            .expireAfterWrite(200, TimeUnit.SECONDS)
            //统计缓存命中率
            .recordStats()
            .build();

    private final static Pattern pattern = Pattern.compile("<(.*?)>");
    private final static String lt = "&lt;";
    private final static String gt = "&gt;";

    @Override
    public GAUGE_AGENT saveAgentName(GAUGE_AGENT_VO gaugeAgentVo) {
        if (StringUtils.isEmpty(gaugeAgentVo.getF_NAME()) || StringUtils.isEmpty(gaugeAgentVo.getF_HARDWARE())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        /*查询名称和硬件id是否同时满足*/
        LambdaQueryWrapper<GAUGE_AGENT> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GAUGE_AGENT::getF_NAME, gaugeAgentVo.getF_NAME())
                .eq(GAUGE_AGENT::getF_HARDWARE, gaugeAgentVo.getF_HARDWARE());
        GAUGE_AGENT one = gaugeAgentService.getOne(wrapper);
        if (one != null) {
            return one;
        }

        /*查询名称是否唯一*/
        LambdaQueryWrapper<GAUGE_AGENT> gaugeAgentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gaugeAgentLambdaQueryWrapper.eq(GAUGE_AGENT::getF_NAME, gaugeAgentVo.getF_NAME());
        List<GAUGE_AGENT> gaugeAgentList = gaugeAgentService.list(gaugeAgentLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(gaugeAgentList))
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_AGENT_NAME_DUPLICATION_EXCEPTION);

        /*查询硬件id是否唯一*/
        LambdaQueryWrapper<GAUGE_AGENT> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GAUGE_AGENT::getF_HARDWARE, gaugeAgentVo.getF_HARDWARE());
        List<GAUGE_AGENT> list = gaugeAgentService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            GAUGE_AGENT gaugeAgent = new GAUGE_AGENT();
            BeanUtils.copyPropertiesIgnoreNull(gaugeAgentVo, gaugeAgent);
            gaugeAgentService.save(gaugeAgent);
            return gaugeAgent;
        } else {
            if (list.size() > 1)
                throw new BusinessException(DataManagementExceptionEnum.GAUGE_AGENT_NAME_DUPLICATION_EXCEPTION);

            GAUGE_AGENT gaugeAgent = list.get(0);
            gaugeAgent.setF_NAME(gaugeAgentVo.getF_NAME());
            gaugeAgentService.updateById(gaugeAgent);
            return gaugeAgent;
        }
    }

    @Override
    public GAUGE_AGENT_DTO findByHardwareId(String hardwareId) {
        LambdaQueryWrapper<GAUGE_AGENT> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GAUGE_AGENT::getF_HARDWARE, hardwareId);
        GAUGE_AGENT one = gaugeAgentService.getOne(queryWrapper);
        if (one == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_HARDWARE_ID_NOT_EXISTS);
        GAUGE_AGENT_DTO gaugeAgentDto = new GAUGE_AGENT_DTO();
        BeanUtils.copyPropertiesIgnoreNull(one, gaugeAgentDto);
        return gaugeAgentDto;
    }

    @Override
    public List<GAUGE_CONNECTION_DTO> getGaugeConfig(Long agentId) {
        LambdaQueryWrapper<GAUGE_CONNECTION> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GAUGE_CONNECTION::getF_GAAG, agentId);
        List<GAUGE_CONNECTION> list = gaugeConnectionService.list(queryWrapper);
        List<GAUGE_CONNECTION_DTO> gaugeConnectionDtoList = GAUGE_CONNECTIONConvert.INSTANCE.convertList(list);
        gaugeConnectionDtoList.forEach(gaugeConnectionDto -> {
            GAUGE_INTERFACE gaugeInterface = gaugeInterfaceService.getById(gaugeConnectionDto.getF_GAIN());
            GAUGE_INTERFACE_DTO gaugeInterfaceDto = new GAUGE_INTERFACE_DTO();
            if (gaugeInterface == null) return;
            BeanUtils.copyPropertiesIgnoreNull(gaugeInterface, gaugeInterfaceDto);
            gaugeConnectionDto.setGaugeInterfaceDto(gaugeInterfaceDto);

            GAUGE_CONNECTION_CONFIG_DTO gaugeConnectionConfigDto =
                    JSONObject.parseObject(gaugeConnectionDto.getF_CONFIG(), GAUGE_CONNECTION_CONFIG_DTO.class);
            gaugeConnectionDto.setGaugeConnectionConfigDto(gaugeConnectionConfigDto);
        });
        return gaugeConnectionDtoList;
    }

    @Override
    public Double serialAnalysis(SerialDebuggingVO serialDebuggingVO) {
        String key = getKey(serialDebuggingVO);
        if (serialDebuggingVO.getType() == 0 && StringUtils.isNotEmpty(serialDebuggingVO.getContent())) {
            String content = serialDebuggingVO.getContent();
            if (StringUtils.isNotEmpty(cache.getIfPresent(key))) {
                content = cache.getIfPresent(key) + content;
            }
            cache.put(key, content);
            return null;
        }

        /*原始数据*/
        String content = cache.getIfPresent(key);
        if (content == null || StringUtils.isEmpty(content)) {
            log.info("{}-------->缓存中没有数据", key);
            return null;
        }
        /*获取对应的量具解析配置*/
        GAUGE_DEVICE gaugeDevice = gaugeDeviceService.getById(serialDebuggingVO.getGaugeDeviceId());
        if (gaugeDevice == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_DEVICE_NOT_EXISTS);
        GAUGE_FORMAT gaugeFormat = gaugeFormatService.getById(gaugeDevice.getF_GAFO());
        if (gaugeFormat == null)
            throw new BusinessException(DataManagementExceptionEnum.GAUGE_FORMAT_NOT_EXISTS);

        GAUGE_FORMAT_ADVANCED_DTO gaugeFormatAdvancedDto = new GAUGE_FORMAT_ADVANCED_DTO();
        if (StringUtils.isNotEmpty(gaugeFormat.getF_ADVANCED())) {
            gaugeFormatAdvancedDto = JSONObject.parseObject(gaugeFormat.getF_ADVANCED(), GAUGE_FORMAT_ADVANCED_DTO.class);
        }

        /*待处理数据*/
        String pending = "";
        if (gaugeFormat.getF_LENGTH() != null && gaugeFormat.getF_LENGTH() != 0) {
            if (gaugeFormat.getF_LENGTH() > content.length()) {
                return null;
            } else {
                pending = content.substring(0, gaugeFormat.getF_LENGTH());
                /*将截取完后的数据更新进缓存*/
                content = content.substring(gaugeFormat.getF_LENGTH());
                if (StringUtils.isNotEmpty(content)) {
                    cache.put(key, content);
                } else {
                    cache.invalidate(key);
                }
            }
        }
        if (StringUtils.isNotEmpty(gaugeFormat.getF_END())) {
            /*正则判断是否包含<>*/
            String endConfig = replace(gaugeFormat.getF_END());
            Matcher matcher = pattern.matcher(endConfig);
            while (matcher.find()) {
                String group = matcher.group(1);
                if (!StringUtils.isNumeric(group))
                    throw new BusinessException(DataManagementExceptionEnum.PLEASE_CHECK_THE_TERMINAL_CONFIGURATION);
                String s = Character.toString((char) Integer.valueOf(group).intValue());
                endConfig = endConfig.replace(matcher.group(), s);
            }
            if (StringUtils.isEmpty(pending)) {
                if (!content.contains(endConfig))
                    throw new BusinessException(DataManagementExceptionEnum.TERMINAL_SYMBOL_NOT_EXISTS);
                pending = content.substring(0, content.indexOf(endConfig));
                /*将截取完后的数据更新进缓存*/
                content = content.substring(content.indexOf(endConfig) + endConfig.length());
                if (StringUtils.isNotEmpty(content)) {
                    cache.put(key, content);
                } else {
                    cache.invalidate(key);
                }
            } else {
                /*说明有长度配置*/
                if (!pending.contains(endConfig))
                    throw new BusinessException(DataManagementExceptionEnum.TERMINAL_SYMBOL_NOT_EXISTS);
                pending = pending.substring(0, pending.indexOf(endConfig));
            }
        }
        if (StringUtils.isEmpty(pending)) {
            /*为空则直接返回*/
            return null;
        }
        if (StringUtils.isNotEmpty(gaugeFormat.getF_START())) {
            /*正则判断是否包含<>*/
            String startConfig = replace(gaugeFormat.getF_START());
            Matcher matcher = pattern.matcher(startConfig);
            while (matcher.find()) {
                String group = matcher.group(1);
                if (!StringUtils.isNumeric(group))
                    throw new BusinessException(DataManagementExceptionEnum.PLEASE_CHECK_THE_INITIATOR_CONFIGURATION);
                String s = Character.toString((char) Integer.valueOf(group).intValue());
                startConfig = startConfig.replace(matcher.group(), s);
            }

            if (pending.contains(startConfig)) {
                pending = pending.substring(pending.indexOf(startConfig));
            }
        }
        String[] split;
        if (StringUtils.isNotEmpty(gaugeFormat.getF_SPLIT())) {
            split = pending.split(gaugeFormat.getF_SPLIT());
        } else {
            split = new String[]{pending};
        }

        List<GAUGE_FORMAT_CONFIG_DTO> gaugeFormatConfigDtoList =
                JSONArray.parseArray(gaugeFormat.getF_DATA_CONFIG(), GAUGE_FORMAT_CONFIG_DTO.class);

        /*获取量具设备配置*/
        GAUGE_DEVICE_CONFIG_DTO gaugeDeviceConfigDto =
                JSONObject.parseObject(gaugeDevice.getF_CONFIG(), GAUGE_DEVICE_CONFIG_DTO.class);


        AtomicReference<Double> A = new AtomicReference<>(0d);
        AtomicReference<Double> B = new AtomicReference<>(0d);
        gaugeFormatConfigDtoList.forEach(gaugeFormatConfigDto -> {
            String val = "";
            try {
                if (gaugeFormatConfigDto.getFieldNum() != null) {
                    val = split[Math.max((gaugeFormatConfigDto.getFieldNum() - 1), 0)];
                }
                if (gaugeFormatConfigDto.getStart() != null) {
                    val = val.substring(Math.max(gaugeFormatConfigDto.getStart() - 1, 0));
                }
                if (gaugeFormatConfigDto.getLength() != null) {
                    val = val.substring(0, gaugeFormatConfigDto.getLength());
                }
            } catch (Exception e) {
                log.info("字符分隔字段数不匹配, 字段号:{},总分隔字段:{}", gaugeFormatConfigDto.getFieldNum(), split.length);
            }
            /*判断频道号*/
            if (StringUtils.isNotEmpty(gaugeDeviceConfigDto.getChannelNum())) {
                if (gaugeFormatConfigDto.getFieldName().contains("频道号") && gaugeFormatConfigDto.getType() == 0) {
                    if (!val.equals(gaugeDeviceConfigDto.getChannelNum())) {
                        log.info("解析频道号和设备配置频道号不匹配!");
                        throw new BusinessException(DataManagementExceptionEnum.THE_CHANNEL_NUMBER_DOES_NOT_MATCH);
                    }
                }
            }
            if (gaugeFormatConfigDto.getType() != null) {
                if (gaugeFormatConfigDto.getType() == 1 && StringUtils.isNumeric(val)) {
                    gaugeFormatConfigDto.setValue(val);
                    if (gaugeFormatConfigDto.getFieldName().contains("A")) {
                        A.set(Double.valueOf(val));
                    } else if (gaugeFormatConfigDto.getFieldName().contains("B")) {
                        B.set(Double.valueOf(val));
                    }
                }
            }

        });
        GAUGE_FORMAT_RETURN_TYPEEnum type = GAUGE_FORMAT_RETURN_TYPEEnum.getType(gaugeFormat.getF_RETURN_TYPE());
        Double val = GAUGE_FORMAT_RETURN_TYPEEnum.getVal(type, A.get(), B.get());

        BigDecimal multiply = BigDecimal.valueOf(val).multiply(BigDecimal.valueOf(gaugeDeviceConfigDto.getFetchValue()));
        BigDecimal subtract = multiply.subtract(BigDecimal.valueOf(gaugeDeviceConfigDto.getZeroValue()));
        if (gaugeDeviceConfigDto.getAbsoluteValue()) {
            subtract = subtract.abs();
        }
        if (gaugeFormatAdvancedDto.getAfterClean()) {
            clean(serialDebuggingVO);
        }
        return subtract.doubleValue();
    }


    private static String getKey(SerialDebuggingVO serialDebuggingVO) {
        return serialDebuggingVO.getHardwareId() + "-" + serialDebuggingVO.getSerialPort();
    }

    @Override
    public void clean(SerialDebuggingVO serialDebuggingVO) {
        String key = getKey(serialDebuggingVO);
        cache.invalidate(key);
    }



    public static String replace(String s) {
        s = s.replace(lt, "<");
        s = s.replace(gt, ">");
        return s;
    }
}

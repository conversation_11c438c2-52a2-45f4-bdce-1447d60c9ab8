package com.yingfei.dataManagement.service.gauge.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.service.gauge.GAUGE_CONNECTIONService;
import com.yingfei.dataManagement.service.gauge.GAUGE_DEVICEService;
import com.yingfei.dataManagement.service.gauge.GAUGE_FORMATService;
import com.yingfei.entity.domain.GAUGE_DEVICE;
import com.yingfei.dataManagement.mapper.GAUGE_DEVICEMapper;
import com.yingfei.entity.dto.GAUGE_CONNECTION_DTO;
import com.yingfei.entity.dto.GAUGE_DEVICE_CONFIG_DTO;
import com.yingfei.entity.dto.GAUGE_DEVICE_DTO;
import com.yingfei.entity.dto.GAUGE_FORMAT_DTO;
import com.yingfei.entity.vo.GAUGE_DEVICE_VO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 针对表【GAUGE_DEVICE(量具设备配置表)】的数据库操作Service实现
 * @createDate 2024-07-30 11:19:11
 */
@Service
public class GAUGE_DEVICEServiceImpl extends ServiceImpl<GAUGE_DEVICEMapper, GAUGE_DEVICE>
        implements GAUGE_DEVICEService {

    @Resource
    private GAUGE_CONNECTIONService gaugeConnectionService;
    @Resource
    private GAUGE_FORMATService gaugeFormatService;

    @Override
    public long getTotal(GAUGE_DEVICE_VO gaugeDeviceVo) {
        return 0;
    }

    @Override
    public List<GAUGE_DEVICE_DTO> getList(GAUGE_DEVICE_VO gaugeDeviceVo) {
        List<GAUGE_DEVICE_DTO> list = baseMapper.getList(gaugeDeviceVo);
        list.forEach(gaugeDeviceDto -> {
            if (StringUtils.isNotEmpty(gaugeDeviceDto.getF_CONFIG())) {
                gaugeDeviceDto.setGaugeDeviceConfigDto(JSONObject.parseObject(gaugeDeviceDto.getF_CONFIG(), GAUGE_DEVICE_CONFIG_DTO.class));
            }
            GAUGE_CONNECTION_DTO connectionDto = gaugeConnectionService.info(gaugeDeviceDto.getF_GICP());
            gaugeDeviceDto.setGaugeConnectionDto(connectionDto);
        });
        return list;
    }

    @Override
    public void add(GAUGE_DEVICE_VO gaugeDeviceVo) {
        GAUGE_DEVICE gaugeDevice = new GAUGE_DEVICE();
        BeanUtils.copyPropertiesIgnoreNull(gaugeDeviceVo, gaugeDevice);
        if (gaugeDeviceVo.getGaugeDeviceConfigDto() != null) {
            gaugeDevice.setF_CONFIG(JSONObject.toJSONString(gaugeDeviceVo.getGaugeDeviceConfigDto()));
        }
        baseMapper.insert(gaugeDevice);
    }

    @Override
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public void checkParam(GAUGE_DEVICE_VO gaugeDeviceVo) {
        if (StringUtils.isEmpty(gaugeDeviceVo.getF_GAFO()) || StringUtils.isEmpty(gaugeDeviceVo.getF_GICP())
                || StringUtils.isEmpty(gaugeDeviceVo.getF_NAME())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
    }

    @Override
    public GAUGE_DEVICE_DTO info(Long id) {
        GAUGE_DEVICE gaugeDevice = baseMapper.selectById(id);
        if (gaugeDevice == null) throw new BusinessException(DataManagementExceptionEnum.GAUGE_DEVICE_NOT_EXISTS);
        GAUGE_DEVICE_DTO gaugeDeviceDto = new GAUGE_DEVICE_DTO();
        BeanUtils.copyPropertiesIgnoreNull(gaugeDevice, gaugeDeviceDto);
        GAUGE_FORMAT_DTO gaugeFormatDto = gaugeFormatService.info(gaugeDeviceDto.getF_GAFO());
        gaugeDeviceDto.setGaugeFormatDto(gaugeFormatDto);
        return gaugeDeviceDto;
    }
}





package com.yingfei.dataManagement.service.gauge;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.GAUGE_AGENT;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.GAUGE_AGENT_DTO;
import com.yingfei.entity.vo.GAUGE_AGENT_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;

/**
* 
* @description 针对表【GAUGE_AGENT(Agent和硬件标识对照表)】的数据库操作Service
* @createDate 2024-07-30 11:19:00
*/
public interface GAUGE_AGENTService extends IService<GAUGE_AGENT>, BaseService<GAUGE_AGENT_VO, GAUGE_AGENT_DTO> {

}

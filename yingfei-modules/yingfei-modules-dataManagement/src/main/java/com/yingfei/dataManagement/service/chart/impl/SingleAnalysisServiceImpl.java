package com.yingfei.dataManagement.service.chart.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.CollectionToMapUtils;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.file.ImageUtils;
import com.yingfei.common.core.utils.poi.ExcelFillIn;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.dataManagement.service.chart.*;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF;
import com.yingfei.entity.domain.BPM_PROCESS_INSTANCE;
import com.yingfei.entity.domain.DESC_DAT;
import com.yingfei.entity.domain.DESC_GRP;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.*;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.enums.TimeEnum;
import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.vo.EVNT_INF_VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SingleAnalysisServiceImpl implements SingleAnalysisService {

    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private HistogramService histogramService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private DESC_DATService descDatService;
    @Resource
    private DESC_GRPService descGrpService;
    @Resource
    private ANALYSIS_DASHBOARD_INFService analysisDashboardInfService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private RedisService redisService;
    @Resource
    private BpmProcessInstanceService bpmProcessInstanceService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;


    @Override
    public Map<String, Object> viewData(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.VIEW_DATA);
        Map<String, Object> map = new HashMap<>();

        Integer isTopOne = analysisChartConfigDTO.getIsTopOne();
        if (isTopOne.equals(YesOrNoEnum.YES.getType())) {
            analysisChartConfigDTO.setIsTopOne(YesOrNoEnum.NO.getType());
        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null) {
            return map;
        }
        analysisChartConfigDTO.setIsTopOne(isTopOne);
        final List<SubgroupDataDTO> cacheSubgroupData = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(cacheSubgroupData)) {
            return map;
        }
        /*动态字段*/
        Set<Object> set = new HashSet<>();

        if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_PART()) ||
                StringUtils.isEmpty(subgroupDataSelectionDTO.getF_TEST()) ||
                StringUtils.isEmpty(subgroupDataSelectionDTO.getF_PRCS()))
            return map;
        return getViewMap(subgroupDataSelectionDTO,cacheSubgroupData, map, set,analysisChartConfigDTO);
    }

    @NotNull
    private Map<String, Object> getViewMap(SubgroupDataSelectionDTO subgroupDataSelectionDTO,List<SubgroupDataDTO> list, Map<String, Object> map, Set<Object> set,AnalysisChartConfigDTO analysisChartConfigDTO) {
//        map.put(Constants.TOTAL, getTotal(subgroupDataSelectionDTO));
//        List<SubgroupDataDTO> list = sgrpInfService.getViewDataSubgroupDataDTOList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(list)) return map;
        /*根据筛选条件 最大返回子组数 截取数据*/
        final List<SubgroupDataDTO> newList = getSubgroupDataDTOS(subgroupDataSelectionDTO, list, analysisChartConfigDTO);

        List<SubgroupDataDTO> reassembly = chartCommonService.reassembly(newList);

        List<SubgroupDataDTO> testValReassembly = chartCommonService.testValReassembly(reassembly);

        List<SubgroupDataDTO> jobList = testValReassembly.stream().filter(s -> StringUtils.isNotEmpty(s.getJobName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(jobList))
            set.add("工作");
        List<SubgroupDataDTO> lotList = testValReassembly.stream().filter(s -> StringUtils.isNotEmpty(s.getLotName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lotList))
            set.add("批次");
        List<SubgroupDataDTO> spltList = testValReassembly.stream().filter(s -> StringUtils.isNotEmpty(s.getShiftName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(spltList))
            set.add("班次");
        List<SubgroupDataDTO> ptrvList = testValReassembly.stream().filter(s -> StringUtils.isNotEmpty(s.getPtrvName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ptrvList))
            set.add("版本");
        List<SubgroupDataDTO> snList = testValReassembly.stream().filter(s -> StringUtils.isNotEmpty(s.getSnName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(snList))
            set.add("序列号");
        List<SGRP_VAL_CHILD_DTO.Test> defList = testValReassembly.stream().map(SubgroupDataDTO::getTestValDto).filter(s -> StringUtils.isNotEmpty(s.getDefectId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(defList))
            set.add("缺陷代码");
        AtomicReference<SubgroupDataDTO> finalSgrpExt = new AtomicReference<>(new SubgroupDataDTO());
        Map<Long, DESC_GRP> dscGrpMap = new HashMap<>();
        Map<Long, DESC_DAT> dscDatMap = new HashMap<>();
        testValReassembly.forEach(a -> {
            finalSgrpExt.set(a);
            if (CollectionUtils.isNotEmpty(a.getSgrpDscList())) {
                Map<String, String> headerMap = new HashMap<>();
                a.getSgrpDscList().forEach(b -> {
                    DESC_GRP descGrp;
                    if (dscGrpMap.get(b.getF_DSGP()) == null) {
                        descGrp = descGrpService.getById(b.getF_DSGP());
                        dscGrpMap.put(b.getF_DSGP(), descGrp);
                        if (descGrp != null)
                            set.add(descGrp.getF_NAME());
                    } else {
                        descGrp = dscGrpMap.get(b.getF_DSGP());
                    }
                    DESC_DAT descDat;
                    if (dscDatMap.get(b.getF_DESC()) == null) {
                        descDat = descDatService.getById(b.getF_DESC());
                        dscDatMap.put(b.getF_DESC(), descDat);
                    } else {
                        descDat = dscDatMap.get(b.getF_DESC());
                    }
                    if (descGrp != null)
                        headerMap.merge(descGrp.getF_NAME(), descDat.getF_NAME(), (a1, b1) -> a1 + Constants.COMMA + b1);
                });
                a.setHeaderMap(headerMap);
            }

            DataSummaryDTO dataSummaryDTO = new DataSummaryDTO();
            SPEC_INF_DTO specLim = specInfService.getSpecLim(dataSummaryDTO, finalSgrpExt.get());
            if (specLim == null) return;
            a.setDataSummaryDTO(dataSummaryDTO);
            if (dataSummaryDTO.getUsl() != null && dataSummaryDTO.getLsl() != null) {
                /*双边公差*/
                if (a.getTestValDto().getTestVal() > dataSummaryDTO.getUsl()) {
                    a.setStatus(1);
                } else if (a.getTestValDto().getTestVal() < dataSummaryDTO.getLsl()) {
                    a.setStatus(2);
                } else {
                    a.setStatus(0);
                }
            } else if (dataSummaryDTO.getUsl() != null) {
                /*单边上公差*/
                if (a.getTestValDto().getTestVal() > dataSummaryDTO.getUsl()) {
                    a.setStatus(1);
                } else {
                    a.setStatus(0);
                }
            } else if (dataSummaryDTO.getLsl() != null) {
                /*单边下公差*/
                if (a.getTestValDto().getTestVal() < dataSummaryDTO.getLsl()) {
                    a.setStatus(2);
                } else {
                    a.setStatus(0);
                }
            }
            log.info("子组信息列表：{}", a.getF_SGRP());
        });
        final Integer offset = subgroupDataSelectionDTO.getOffset();
        final Integer next = subgroupDataSelectionDTO.getNext();
        // 若 offset 大于等于列表长度，说明分页数超出可分页数，直接返回空列表
        if (offset >= testValReassembly.size()) {
            map.put(Constants.TOTAL, testValReassembly.size());
            map.put(Constants.LIST, Collections.emptyList());
            map.put(Constants.SET, set);
            return map;
        }

        // 计算分页截取的结束索引
        int endIndex = Math.min(offset + next, testValReassembly.size());

        // 进行分页截取
        List<SubgroupDataDTO> pagedList = testValReassembly.subList(offset, endIndex);
        map.put(Constants.TOTAL, testValReassembly.size());
        map.put(Constants.LIST, pagedList);
        map.put(Constants.SET, set);
        return map;
    }

    @Override
    public Map<String, Object> viewDataList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.VIEW_DATA);

        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        Map<String, Object> map = new HashMap<>();

        /*动态字段*/
        Set<Object> set = new HashSet<>();
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null) {
            return map;
        }
        final List<SubgroupDataDTO> cacheSubgroupData = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(cacheSubgroupData)) {
            return map;
        }
        return getViewMap(subgroupDataSelectionDTO,cacheSubgroupData, map, set,analysisChartConfigDTO);
    }


    @Override
    public Map<String, Object> exceptionSummary(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.EXCEPTION_SUMMARY);
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        Map<String, Object> map = new HashMap<>();
        if (subgroupDataSelectionDTO == null) return map;
        /*获取子组列表*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        /*根据筛选条件 最大返回子组数 截取数据*/
        final List<SubgroupDataDTO> newList = getSubgroupDataDTOS(subgroupDataSelectionDTO, subgroupDataDTOList, analysisChartConfigDTO);

        if (CollectionUtils.isEmpty(newList)) return map;

        EVNT_INF_VO evntInfVo = new EVNT_INF_VO();
        evntInfVo.setNext(subgroupDataSelectionDTO.getNext());
        evntInfVo.setOffset(subgroupDataSelectionDTO.getOffset());
        evntInfVo.setF_STATUS(subgroupDataSelectionDTO.getEvntStatus());
        evntInfVo.setDbType(InitConfig.getDriverType());
        evntInfVo.setSgrpInfList(newList.stream().map(SubgroupDataDTO::getF_SGRP).collect(Collectors.toList()));
        List<EVNT_INF_DTO> evntInfDtoList = evntInfService.getList(evntInfVo);
        if (CollectionUtils.isNotEmpty(evntInfDtoList)) {
            evntInfDtoList.forEach(evntInfDto -> {
                LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_EVNT, evntInfDto.getF_EVNT());
                List<BPM_PROCESS_INSTANCE> list = bpmProcessInstanceService.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    evntInfDto.setBpmProcessInstanceList(list);
                }
            });
        }
        map.put(Constants.TOTAL, evntInfService.getTotal(evntInfVo));
        map.put(Constants.LIST, evntInfDtoList);
        return map;
    }

    private List<SubgroupDataDTO> getSubgroupDataDTOS(SubgroupDataSelectionDTO subgroupDataSelectionDTO, List<SubgroupDataDTO> subgroupDataDTOList, AnalysisChartConfigDTO analysisChartConfigDTO) {

        if(CollectionUtils.isEmpty(subgroupDataDTOList)){
            return Collections.emptyList();
        }
        List<SubgroupDataDTO> newList = new ArrayList<>();
        if (analysisChartConfigDTO.getIsTopOne() == YesOrNoEnum.NO.getType() && subgroupDataSelectionDTO.getIsRange() == YesOrNoEnum.NO.getType()){
            newList.addAll(chartCommonService.getTotalNumSubgroup(subgroupDataDTOList, subgroupDataSelectionDTO.getMaxNum(), 2));
        }else{
            if (subgroupDataSelectionDTO.getIsRange() == YesOrNoEnum.NO.getType()) {
                subgroupDataSelectionDTO.setPartList(Collections.singletonList(subgroupDataSelectionDTO.getF_PART()));
                subgroupDataSelectionDTO.setPtrvList(Collections.singletonList(subgroupDataSelectionDTO.getF_REV()));
                subgroupDataSelectionDTO.setPrcsList(Collections.singletonList(subgroupDataSelectionDTO.getF_PRCS()));
                subgroupDataSelectionDTO.setTestList(Collections.singletonList(subgroupDataSelectionDTO.getF_TEST()));
            }
            final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                    subgroupDataSelectionDTO.getPartList(),
                    subgroupDataSelectionDTO.getPtrvList(),
                    subgroupDataSelectionDTO.getPrcsList(),
                    subgroupDataSelectionDTO.getTestList());

            for (Map<String, Long> mapData : queryMapList) {
                // 进行空值检查
                if (mapData == null) {
                    continue;
                }
                Long fpart = mapData.get(Constants.part);
                Long fptrv = mapData.get(Constants.ptrv);
                Long fprcs = mapData.get(Constants.prcs);
                Long ftest = mapData.get(Constants.test);
                // 过滤符合条件的 SubgroupDataDTO 列表
                final List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                        .filter(x -> fpart != null && fpart.equals(x.getF_PART()) &&
                                fptrv != null && fptrv.equals(x.getF_REV()) &&
                                fprcs != null && fprcs.equals(x.getF_PRCS()) &&
                                ftest != null && ftest.equals(x.getF_TEST()))
                        .collect(Collectors.toList());
                // 当 collect 列表为空时，跳过当前循环
                if (CollectionUtils.isEmpty(collect)) {
                    continue;
                }
                newList.addAll(chartCommonService.getTotalNumSubgroup(collect, subgroupDataSelectionDTO.getMaxNum(), 2));
            }
        }
        return newList;
    }

    @Override
    public Map<String, Object> exceptionSummaryList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.EXCEPTION_SUMMARY);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        Map<String, Object> map = new HashMap<>();

        EVNT_INF_VO evntInfVo = new EVNT_INF_VO();
        evntInfVo.setNext(subgroupDataSelectionDTO.getNext());
        evntInfVo.setOffset(subgroupDataSelectionDTO.getOffset());
        evntInfVo.setF_STATUS(subgroupDataSelectionDTO.getEvntStatus());
        evntInfVo.setDbType(InitConfig.getDriverType());
        evntInfVo.setPartList(subgroupDataSelectionDTO.getPartList());
        evntInfVo.setPrcsList(subgroupDataSelectionDTO.getPrcsList());
        evntInfVo.setTestList(subgroupDataSelectionDTO.getTestList());
        List<EVNT_INF_DTO> evntInfDtoList = evntInfService.getList(evntInfVo);
        if (CollectionUtils.isNotEmpty(evntInfDtoList)) {
            evntInfDtoList.forEach(evntInfDto -> {
                LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_EVNT, evntInfDto.getF_EVNT());
                List<BPM_PROCESS_INSTANCE> list = bpmProcessInstanceService.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    evntInfDto.setBpmProcessInstanceList(list);
                }
            });
        }
        map.put(Constants.TOTAL, evntInfService.getTotal(evntInfVo));
        map.put(Constants.LIST, evntInfDtoList);
        return map;
    }

    @Override
    public long getTotal(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        return sgrpInfService.getViewDataTotal(subgroupDataSelectionDTO);
    }

    @Override
    public SubgroupDataSelectionDTO getSearch(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        /*根据菜单获取分析模板配置*/
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(subgroupDataSelectionDTO.getMenuId());
        subgroupDataSelectionDTO.setParameterId(analysisDashboardInf.getF_PRST());
        AnalysisChartConfigDTO analysisChartConfigDTO = new AnalysisChartConfigDTO();
        analysisChartConfigDTO.setIsTopOne(0);
        subgroupDataSelectionDTO = histogramService.getDataSelection(subgroupDataSelectionDTO, 1, analysisChartConfigDTO);
        return subgroupDataSelectionDTO;
    }

    @Override
    public List<STREAM_TREND_INF_DTO> realTimeCapabilityTrend(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.REAL_TIME_CAPABILITY_TREND);
        RealTimeCapabilityTrendConfigDTO realTimeCapabilityTrendConfigDTO;
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), RealTimeCapabilityTrendConfigDTO.class);
        } else {
            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), RealTimeCapabilityTrendConfigDTO.class);
        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);

        /*获取子组列表*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        /*将失效子组排除*/
        subgroupDataDTOList = subgroupDataDTOList.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());

        final Map<String, Object> objectMap = getStreamTrendInfs(subgroupDataDTOList,subgroupDataSelectionDTO, realTimeCapabilityTrendConfigDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList) || ObjectUtils.isEmpty(objectMap)) {
            return Collections.emptyList();
        }
        final List<STREAM_TREND_INF_DTO> infoList = ObjectUtils.isEmpty(objectMap.get("infoList")) ? null : JSONArray.parseArray(JSON.toJSONString(objectMap.get("infoList")), STREAM_TREND_INF_DTO.class);
        final JSONObject infoCondition = JSONObject.parse(JSON.toJSONString(objectMap.get("infoCondition")));

        final StreamTrendInfCatchData1DTO streamTrendInfCatchData1DTO = new StreamTrendInfCatchData1DTO();
        streamTrendInfCatchData1DTO.setInfoList(infoList);
        final String prcsName = infoCondition.getString("prcsName");
        final String partName = infoCondition.getString("partName");
        final String testName = infoCondition.getString("testName");
        streamTrendInfCatchData1DTO.setPartName(partName);
        streamTrendInfCatchData1DTO.setPrcsName(prcsName);
        streamTrendInfCatchData1DTO.setTestName(testName);

        final StreamTrendInfCatchData2DTO streamTrendInfCatchData2Dto = ObjectUtils.isEmpty(objectMap.get("catchList")) ? null : JSONObject.parseObject(JSON.toJSONString(objectMap.get("catchList")), StreamTrendInfCatchData2DTO.class);
        String key = String.format(RedisConstant.STREAM_TREND_DATA, subgroupDataSelectionDTO.getParameterId());
        String key2 = String.format(RedisConstant.STREAM_TREND_DATA2, subgroupDataSelectionDTO.getParameterId());
        String conditionKey = String.format(RedisConstant.STREAM_TREND_CONDITION, subgroupDataSelectionDTO.getParameterId());
        redisService.deleteObject(key);
        redisService.deleteObject(key2);
        if (CollectionUtils.isNotEmpty(infoList) && ObjectUtils.isNotEmpty(streamTrendInfCatchData2Dto)) {
            redisService.setCacheList(key, Collections.singletonList(streamTrendInfCatchData1DTO), Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
            redisService.setCacheList(key2, Collections.singletonList(streamTrendInfCatchData2Dto), Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
            Map<String, String> map = new HashMap<>(5);
            map.put("产品", partName);
            map.put("过程", prcsName);
            map.put("测试", testName);
            map.put("时间范围", realTimeCapabilityTrendConfigDTO.getGroupType() == 1 ?
                    DateUtils.dateTimeTwo(infoList.get(0).getF_END()) + "-" + DateUtils.dateTimeTwo(infoList.get(infoList.size() - 1).getF_END()) :
                    DateUtils.dateTimeTwo(subgroupDataDTOList.get(0).getF_SGTM()) + "-" + DateUtils.dateTimeTwo(subgroupDataDTOList.get(subgroupDataDTOList.size() - 1).getF_SGTM()));
            System.out.println(JSON.toJSONString(analysisChartConfigDTO.getChartConfig()));

            map.put("数据统计方式", realTimeCapabilityTrendConfigDTO.getGroupType() == 1 ?
                    "基于时间分组 (时间间隔:" + realTimeCapabilityTrendConfigDTO.getTimeInterval() + TimeEnum.getType(realTimeCapabilityTrendConfigDTO.getTimeType()).getDesc() + ")" :
                    "基于子组数量(分组数:" + realTimeCapabilityTrendConfigDTO.getGroupNum() + " 偏移量:" + realTimeCapabilityTrendConfigDTO.getOffset() + ")");
            redisService.setCacheObject(conditionKey, map, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
        }
        return infoList;
    }

    @Override
    public List<List<STREAM_TREND_INF_DTO>> realTimeCapabilityTrendList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.REAL_TIME_CAPABILITY_TREND);

        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        RealTimeCapabilityTrendConfigDTO realTimeCapabilityTrendConfigDTO;
        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), RealTimeCapabilityTrendConfigDTO.class);
        } else {
            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), RealTimeCapabilityTrendConfigDTO.class);
        }

        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);


        /*将失效子组排除*/
        subgroupDataDTOList = subgroupDataDTOList.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType()).collect(Collectors.toList());

        /*多测试拆分*/
        subgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                subgroupDataSelectionDTO.getPartList(),
                subgroupDataSelectionDTO.getPtrvList(),
                subgroupDataSelectionDTO.getPrcsList(),
                subgroupDataSelectionDTO.getTestList());
        if (CollectionUtils.isEmpty(subgroupDataDTOList) || CollectionUtils.isEmpty(queryMapList)) {
            return Collections.emptyList();
        }

        List<Future<Map<String, Object>>> futures = new ArrayList<>();

        for (Map<String, Long> map : queryMapList) {
            // 进行空值检查
            if (map == null) {
                continue;
            }
            Long fpart = map.get(Constants.part);
            Long fptrv = map.get(Constants.ptrv);
            Long fprcs = map.get(Constants.prcs);
            Long ftest = map.get(Constants.test);
            // 过滤符合条件的 SubgroupDataDTO 列表
            final List<SubgroupDataDTO> collect = subgroupDataDTOList.stream()
                    .filter(x -> fpart != null && fpart.equals(x.getF_PART()) &&
                            fptrv != null && fptrv.equals(x.getF_REV()) &&
                            fprcs != null && fprcs.equals(x.getF_PRCS()) &&
                            ftest != null && ftest.equals(x.getF_TEST()))
                    .collect(Collectors.toList());

            // 当 collect 列表为空时，跳过当前循环
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }

            // 提交任务到线程池
            futures.add(threadPoolConfig.threadPoolTaskExecutor().submit(() -> getStreamTrendInfs(collect,subgroupDataSelectionDTO, realTimeCapabilityTrendConfigDTO, analysisChartConfigDTO)));
        }
        List<Map<String, Object>> result = new ArrayList<>();
        // 获取任务结果
        for (Future<Map<String, Object>> future : futures) {
            try {
                Map<String, Object> dto = future.get();
                if (!ObjectUtils.isEmpty(dto)) {
                    result.add(dto);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程处理出错", e);
            }
        }
        String key = String.format(RedisConstant.STREAM_TREND_DATA, subgroupDataSelectionDTO.getParameterId());
        String key2 = String.format(RedisConstant.STREAM_TREND_DATA2, subgroupDataSelectionDTO.getParameterId());
        String conditionKey = String.format(RedisConstant.STREAM_TREND_CONDITION, subgroupDataSelectionDTO.getParameterId());
        redisService.deleteObject(key);
        redisService.deleteObject(key2);
        List<List<STREAM_TREND_INF_DTO>> list = new ArrayList<>();
        List<StreamTrendInfCatchData1DTO> list1 = new ArrayList<>();
        List<StreamTrendInfCatchData2DTO> list2 = new ArrayList<>();
        Set<String> partNameSet = new HashSet<>();
        Set<String> prcsNameSet = new HashSet<>();
        Set<String> testNameSet = new HashSet<>();
        for (Map<String, Object> map : result) {
            if (ObjectUtils.isEmpty(map)) {
                continue;
            }
            final JSONObject infoCondition = JSONObject.parse(JSONObject.toJSONString(map.get("infoCondition")));
            final StreamTrendInfCatchData1DTO streamTrendInfCatchData1DTO = new StreamTrendInfCatchData1DTO();
            streamTrendInfCatchData1DTO.setPartName(infoCondition.get("partName") + "");
            streamTrendInfCatchData1DTO.setPrcsName(infoCondition.get("prcsName") + "");
            streamTrendInfCatchData1DTO.setTestName(infoCondition.get("testName") + "");
            partNameSet.add(infoCondition.get("partName") + "");
            prcsNameSet.add(infoCondition.get("prcsName") + "");
            testNameSet.add(infoCondition.get("testName") + "");
            streamTrendInfCatchData1DTO.setInfoList(JSONArray.parseArray(JSON.toJSONString(map.get("infoList")), STREAM_TREND_INF_DTO.class));
            final List<STREAM_TREND_INF_DTO> infoList = JSONArray.parseArray(JSON.toJSONString(map.get("infoList")), STREAM_TREND_INF_DTO.class);

            //如果仅一个数据点，且数据点对应的参数值为空时，直接显示无数据
            boolean a = true;
            if(infoList.size() == 1){
                //cp
                if(realTimeCapabilityTrendConfigDTO.getShowType() == 1 && ObjectUtils.isEmpty(infoList.get(0).getF_CP())){
                    a = false;
                }
                //cpk
                if(realTimeCapabilityTrendConfigDTO.getShowType() == 2 && ObjectUtils.isEmpty(infoList.get(0).getF_CPK())){
                    a = false;
                }
            }
            if(a) {
                list.add(infoList);
                list1.add(streamTrendInfCatchData1DTO);
                list2.add(JSONObject.parseObject(JSON.toJSONString(map.get("catchList")), StreamTrendInfCatchData2DTO.class));
            }
        }
        if (CollectionUtils.isNotEmpty(list1)) {
            redisService.setCacheList(key, list1, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
            redisService.setCacheList(key2, list2, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
            Map<String, String> map = new HashMap<>();
            map.put("产品", String.join(Constants.COMMA, partNameSet));
            map.put("过程", String.join(Constants.COMMA, prcsNameSet));
            map.put("测试", String.join(Constants.COMMA, testNameSet));
            map.put("时间范围", realTimeCapabilityTrendConfigDTO.getGroupType() == 1 ?
                    DateUtils.dateTimeTwo(list1.get(0).getInfoList().get(0).getF_END()) + "-" + DateUtils.dateTimeTwo(list1.get(0).getInfoList().get(list1.get(0).getInfoList().size() - 1).getF_END()) :
                    DateUtils.dateTimeTwo(subgroupDataDTOList.get(0).getF_SGTM()) + "-" + DateUtils.dateTimeTwo(subgroupDataDTOList.get(subgroupDataDTOList.size() - 1).getF_SGTM()));
            map.put("数据统计方式", realTimeCapabilityTrendConfigDTO.getGroupType() == 1 ?
                    "基于时间分组 (时间间隔:" + realTimeCapabilityTrendConfigDTO.getTimeInterval() + TimeEnum.getType(realTimeCapabilityTrendConfigDTO.getTimeType()).getDesc() + ")" :
                    "基于子组数量(分组数:" + realTimeCapabilityTrendConfigDTO.getGroupNum() + " 偏移量:" + realTimeCapabilityTrendConfigDTO.getOffset() + ")");
            redisService.setCacheObject(conditionKey, map, Constants.ACCOUNT_LOGIN_FAILURES_LOCK_TIME);
        }
        return list;
    }


    private Map<String, Object> getStreamTrendInfs(List<SubgroupDataDTO> subgroupDataDTOList,SubgroupDataSelectionDTO subgroupDataSelectionDTO, RealTimeCapabilityTrendConfigDTO realTimeCapabilityTrendConfigDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {

        subgroupDataDTOList = chartCommonService.getTotalNumSubgroup(subgroupDataDTOList, analysisChartConfigDTO.getMaxNum(),2);

        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return null;
        }
        //判断是否有公差限
        SubgroupDataDTO subgroupDataDTO1 = new SubgroupDataDTO();
        subgroupDataDTO1.setF_PART(subgroupDataDTOList.get(0).getF_PART());
        subgroupDataDTO1.setF_PRCS(subgroupDataDTOList.get(0).getF_PRCS());
        subgroupDataDTO1.setF_TEST(subgroupDataDTOList.get(0).getF_TEST());
        subgroupDataDTO1.setF_JOB(subgroupDataDTOList.get(0).getF_JOB());
        subgroupDataDTO1.setF_REV(subgroupDataDTOList.get(0).getF_REV());
        SPEC_INF_DTO specInf = specInfService.getSpecLim(subgroupDataDTO1);

        if (ObjectUtils.isEmpty(specInf)) {
            return null;
        }

        /*按子组时间正序*/
        subgroupDataDTOList = subgroupDataDTOList.stream()
                .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP))
                .collect(Collectors.toList());

        List<STREAM_TREND_INF_DTO> infoList = new ArrayList<>();
        List<StreamTrendInfCatchDTO> catchList = new ArrayList<>();
        List<List<String>> groupByList = new ArrayList<>();
        HashMap<String, Object> listMap = new HashMap<>();
        HashMap<String, Object> resultMap = new HashMap<>(3);
        Set<String> partNameSet = new HashSet<>();
        Set<String> prcsNameSet = new HashSet<>();
        Set<String> testNameSet = new HashSet<>();
        if (realTimeCapabilityTrendConfigDTO.getGroupType() == 1) {
            /*获取开始时间*/
            Date startDate = subgroupDataDTOList.get(0).getF_SGTM();
            long startTime = startDate.getTime();
            /*获取结束时间*/
            long endTime = subgroupDataDTOList.get(subgroupDataDTOList.size() - 1).getF_SGTM().getTime();
            /*间隔*/
            long interval = 0L;
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 获取当前分钟数
            int minutes = calendar.get(Calendar.MINUTE);
            int seconds = calendar.get(Calendar.SECOND);
            int milliseconds = calendar.get(Calendar.MILLISECOND);
            switch (TimeEnum.getType(realTimeCapabilityTrendConfigDTO.getTimeType())) {
                case HOUR:
                    interval = Constants.HOUR * realTimeCapabilityTrendConfigDTO.getTimeInterval();
                    /*将不足一小时补足*/
                    if (minutes != 0 || seconds != 0 || milliseconds != 0) {
                        // 增加一个小时
                        calendar.add(Calendar.HOUR_OF_DAY, 1);
                        // 设置分钟为0
                        calendar.set(Calendar.MINUTE, 0);
                        // 设置秒为0
                        calendar.set(Calendar.SECOND, 0);
                        // 设置毫秒为0
                        calendar.set(Calendar.MILLISECOND, 0);
                        startDate = calendar.getTime();
                        startTime = startDate.getTime();
                    } else {
                        startTime = startTime + interval;
                    }
                    break;
                case DAY:
                    interval = Constants.DAY * realTimeCapabilityTrendConfigDTO.getTimeInterval();
                    /*将不足一天补足*/
                    if (minutes != 0 || seconds != 0 || milliseconds != 0) {
                        // 增加一天
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                        // 设置小时为0
                        calendar.set(Calendar.HOUR, 0);
                        // 设置分钟为0
                        calendar.set(Calendar.MINUTE, 0);
                        // 设置秒为0
                        calendar.set(Calendar.SECOND, 0);
                        // 设置毫秒为0
                        calendar.set(Calendar.MILLISECOND, 0);
                        startDate = calendar.getTime();
                        startTime = startDate.getTime();
                    } else {
                        startTime = startTime + interval;
                    }
                    break;
            }
            if (interval == 0L) return null;
            long max = (endTime - startTime) / interval;
            Map<Long, SubgroupDataDTO> map = CollectionToMapUtils.convertMap(subgroupDataDTOList, SubgroupDataDTO::getF_SGRP);
            long end = 0L;
            long start = 0L;
            for (int i = 0; ; i++) {
                if (MapUtils.isEmpty(map)) break;
                if (i > max + 100) {
                    /*防止死循环*/
                    break;
                }
                if (i == 0) {
                    end = startTime;
                } else {
                    start = end;
                    end += interval;
                }
                long finalEnd = end;
                long finalStart = start;
                List<SubgroupDataDTO> collect = subgroupDataDTOList.stream().filter(s -> s.getF_SGTM().getTime() >= finalStart && s.getF_SGTM().getTime() <= finalEnd).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) continue;
                if (map.get(collect.get(0).getF_SGRP()) == null) continue;
                collect.forEach(subgroupDataDTO -> {
                    map.remove(subgroupDataDTO.getF_SGRP());
                });
                /*通过测试数据重新组装子组*/
                List<SubgroupDataDTO> reassemblySubgroupDataDTOList = chartCommonService.reassembly(collect);
                partNameSet.addAll(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getPartName).collect(Collectors.toList()));
                prcsNameSet.addAll(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getPrcsName).collect(Collectors.toList()));
                testNameSet.addAll(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getTestName).collect(Collectors.toList()));
                /*计算cp,cpk*/
                DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(reassemblySubgroupDataDTOList, 1, 0);
                /*获取公差限*/
                SPEC_INF_DTO specInfDto = specInfService.getSpecLim(dataSummaryDTO, reassemblySubgroupDataDTOList.get(0));
                /*获取Cp*/
                dataSummaryDTO.setCp(HistogramUtil.getCp(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                        dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
                /*获取Cpk*/
                dataSummaryDTO.setCpk(HistogramUtil.getCpk(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                        dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
                STREAM_TREND_INF_DTO streamTrendInfDto = new STREAM_TREND_INF_DTO();
                streamTrendInfDto
                        .setF_CP(dataSummaryDTO.getCp() == null ? null :
                                BigDecimal.valueOf(dataSummaryDTO.getCp()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_TAR(dataSummaryDTO.getTargetValue() == null ? null :
                                BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_CPTAR(specInfDto == null ? null : specInfDto.getF_CP() == null ? null :
                                BigDecimal.valueOf(specInfDto.getF_CP()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_CPKTAR(specInfDto == null ? null : specInfDto.getF_CPK() == null ? null :
                                BigDecimal.valueOf(specInfDto.getF_CPK()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_PART(reassemblySubgroupDataDTOList.get(0).getF_PART())
                        .setF_PTRV(reassemblySubgroupDataDTOList.get(0).getF_REV())
                        .setF_PRCS(reassemblySubgroupDataDTOList.get(0).getF_PRCS())
                        .setF_TEST(reassemblySubgroupDataDTOList.get(0).getF_TEST())
                        .setF_RANGE_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getRange() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getRange).sum())
                        .setF_SD_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getSd() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getSd).sum())
                        .setF_VALUE_COUNT(dataSummaryDTO.getSubGroupNum() * dataSummaryDTO.getSubGroupSize().intValue()).setF_START(new Date(start)).setF_END(new Date(end));
                streamTrendInfDto.setPartName(reassemblySubgroupDataDTOList.get(0).getPartName());
                streamTrendInfDto.setPrcsName(reassemblySubgroupDataDTOList.get(0).getPrcsName());
                streamTrendInfDto.setPtrvName(reassemblySubgroupDataDTOList.get(0).getPtrvName());
                streamTrendInfDto.setTestName(reassemblySubgroupDataDTOList.get(0).getTestName());
                infoList.add(streamTrendInfDto);

                List<SubgroupDataDTO> testValReassembly = chartCommonService.testValReassembly(reassemblySubgroupDataDTOList);
                final List<String> groupByFSGRPs = reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SGRP).distinct().map(String::valueOf).collect(Collectors.toList());
                groupByList.add(groupByFSGRPs);
                for (SubgroupDataDTO subgroupDataDTO : testValReassembly) {
                    final SGRP_VAL_CHILD_DTO.Test testValDto = subgroupDataDTO.getTestValDto();
                    final List<SGRP_VAL_CHILD_DTO.SubTest> subTestList = testValDto.getSubTestList();
                    if (CollectionUtils.isEmpty(subTestList)) {
                        StreamTrendInfCatchDTO streamTrendInfCatch = new StreamTrendInfCatchDTO();
                        streamTrendInfCatch
                                .setF_CP(dataSummaryDTO.getCp() == null ? null :
                                        BigDecimal.valueOf(dataSummaryDTO.getCp()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                        BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue() + "")
                                .setF_TAR(dataSummaryDTO.getTargetValue() == null ? null :
                                        BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_CPTAR(specInfDto == null ? null : specInfDto.getF_CP() == null ? null :
                                        BigDecimal.valueOf(specInfDto.getF_CP()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_CPKTAR(specInfDto == null ? null : specInfDto.getF_CPK() == null ? null :
                                        BigDecimal.valueOf(specInfDto.getF_CPK()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_RANGE_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getRange() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getRange).sum())
                                .setF_SD_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getSd() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getSd).sum())
                                .setF_VALUE_COUNT(dataSummaryDTO.getSubGroupNum() * dataSummaryDTO.getSubGroupSize().intValue()).setF_START(new Date(start)).setF_END(new Date(end))
                                .setF_SGRP(subgroupDataDTO.getF_SGRP())
                                .setPartName(subgroupDataDTO.getPartName())
                                .setRevName(subgroupDataDTO.getPtrvName())
                                .setPrcsName(subgroupDataDTO.getPrcsName())
                                .setTestName(subgroupDataDTO.getTestName())
                                .setShiftName(subgroupDataDTO.getShiftName())
                                .setLotName(subgroupDataDTO.getLotName())
                                .setJobName(subgroupDataDTO.getJobName())
                                .setSgrpDscList(subgroupDataDTO.getSgrpDscList())
                                .setF_SGTM(subgroupDataDTO.getF_SGTM())
                                .setTestVal(testValDto.getTestVal())
                                .setTestNo(testValDto.getTestNo() + 1)
                        ;
                        catchList.add(streamTrendInfCatch);
                    } else {
                        for (SGRP_VAL_CHILD_DTO.SubTest subTest : subTestList) {
                            StreamTrendInfCatchDTO streamTrendInfCatch = new StreamTrendInfCatchDTO();
                            streamTrendInfCatch
                                    .setF_CP(dataSummaryDTO.getCp() == null ? null :
                                            BigDecimal.valueOf(dataSummaryDTO.getCp()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                            BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue() + "")
                                    .setF_TAR(dataSummaryDTO.getTargetValue() == null ? null :
                                            BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_CPTAR(specInfDto == null ? null : specInfDto.getF_CP() == null ? null :
                                            BigDecimal.valueOf(specInfDto.getF_CP()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_CPKTAR(specInfDto == null ? null : specInfDto.getF_CPK() == null ? null :
                                            BigDecimal.valueOf(specInfDto.getF_CPK()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_RANGE_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getRange() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getRange).sum())
                                    .setF_SD_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getSd() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getSd).sum())
                                    .setF_VALUE_COUNT(dataSummaryDTO.getSubGroupNum() * dataSummaryDTO.getSubGroupSize().intValue()).setF_START(new Date(start)).setF_END(new Date(end))
                                    .setF_SGRP(subgroupDataDTO.getF_SGRP())
                                    .setPartName(subgroupDataDTO.getPartName())
                                    .setRevName(subgroupDataDTO.getPtrvName())
                                    .setPrcsName(subgroupDataDTO.getPrcsName())
                                    .setTestName(subgroupDataDTO.getTestName())
                                    .setShiftName(subgroupDataDTO.getShiftName())
                                    .setLotName(subgroupDataDTO.getLotName())
                                    .setJobName(subgroupDataDTO.getJobName())
                                    .setSgrpDscList(subgroupDataDTO.getSgrpDscList())
                                    .setF_SGTM(subgroupDataDTO.getF_SGTM())
                                    .setTestVal(subTest.getSubTestValue())
                                    .setSubTestNo(subTest.getSubTestNo() + 1)
                                    .setTestNo(testValDto.getTestNo() + 1);
                            catchList.add(streamTrendInfCatch);
                        }
                    }
                }
                if (finalEnd > endTime) {
                    break;
                }
            }
        } else {
            /*按分组数量拆分*/
            List<List<SubgroupDataDTO>> partition;
            if (realTimeCapabilityTrendConfigDTO.getOffset() == 0 || (subgroupDataDTOList.size() < realTimeCapabilityTrendConfigDTO.getGroupNum() ||
                    subgroupDataDTOList.size() < (realTimeCapabilityTrendConfigDTO.getGroupNum() + realTimeCapabilityTrendConfigDTO.getOffset()))) {
                partition = Lists.partition(subgroupDataDTOList, realTimeCapabilityTrendConfigDTO.getGroupNum());
            } else {
                partition = partitionWithOffset(subgroupDataDTOList, realTimeCapabilityTrendConfigDTO.getGroupNum(), realTimeCapabilityTrendConfigDTO.getOffset());
            }

            SPEC_INF_DTO specInfDto = null;
            int size = 1;

            for (List<SubgroupDataDTO> dataDTOList : partition) {
                /*通过测试数据重新组装子组*/
                List<SubgroupDataDTO> reassemblySubgroupDataDTOList = chartCommonService.reassembly(dataDTOList);
                partNameSet.addAll(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getPartName).collect(Collectors.toList()));
                prcsNameSet.addAll(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getPrcsName).collect(Collectors.toList()));
                testNameSet.addAll(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getTestName).collect(Collectors.toList()));
                final List<String> groupByFSGRPs = reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getF_SGRP).distinct().map(String::valueOf).collect(Collectors.toList());
                groupByList.add(groupByFSGRPs);
                /*计算cp,cpk*/
                DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(reassemblySubgroupDataDTOList, 1, 0);
                /*获取公差限*/
                if (specInfDto == null) {
                    specInfDto = specInfService.getSpecLim(dataSummaryDTO, reassemblySubgroupDataDTOList.get(0));
                } else {
                    DataSummaryDTO.setSpec(dataSummaryDTO, specInfDto);
                }

                /*获取Cp*/
                dataSummaryDTO.setCp(HistogramUtil.getCp(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                        dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
                /*获取Cpk*/
                dataSummaryDTO.setCpk(HistogramUtil.getCpk(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                        dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
                STREAM_TREND_INF_DTO streamTrendInfDto = new STREAM_TREND_INF_DTO();
                streamTrendInfDto
                        .setF_CP(dataSummaryDTO.getCp() == null ? null :
                                BigDecimal.valueOf(dataSummaryDTO.getCp()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_TAR(dataSummaryDTO.getTargetValue() == null ? null :
                                BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_CPTAR(specInfDto == null ? null : specInfDto.getF_CP() == null ? null :
                                BigDecimal.valueOf(specInfDto.getF_CP()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_CPKTAR(specInfDto == null ? null : specInfDto.getF_CPK() == null ? null :
                                BigDecimal.valueOf(specInfDto.getF_CPK()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                        .setF_PART(reassemblySubgroupDataDTOList.get(0).getF_PART())
                        .setF_PTRV(reassemblySubgroupDataDTOList.get(0).getF_REV())
                        .setF_PRCS(reassemblySubgroupDataDTOList.get(0).getF_PRCS())
                        .setF_TEST(reassemblySubgroupDataDTOList.get(0).getF_TEST())
                        .setF_RANGE_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getRange() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getRange).sum())
                        .setF_SD_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getSd() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getSd).sum())
                        .setF_VALUE_COUNT(dataSummaryDTO.getSubGroupNum() * dataSummaryDTO.getSubGroupSize().intValue());
                streamTrendInfDto.setPartName(reassemblySubgroupDataDTOList.get(0).getPartName());
                streamTrendInfDto.setPrcsName(reassemblySubgroupDataDTOList.get(0).getPrcsName());
                streamTrendInfDto.setPtrvName(reassemblySubgroupDataDTOList.get(0).getPtrvName());
                streamTrendInfDto.setTestName(reassemblySubgroupDataDTOList.get(0).getTestName());
                infoList.add(streamTrendInfDto);
                List<SubgroupDataDTO> testValReassembly = chartCommonService.testValReassembly(reassemblySubgroupDataDTOList);
                List<Long> offsetIdList = new ArrayList<>();
                if (realTimeCapabilityTrendConfigDTO.getOffset() > 0 && size == 1 && partition.size() > 1) {
                    offsetIdList = reassemblySubgroupDataDTOList.subList(0, reassemblySubgroupDataDTOList.size() - 1).stream()
                            .map(SubgroupDataDTO::getF_SGRP)
                            .collect(Collectors.toList());
                }
                for (SubgroupDataDTO subgroupDataDTO : testValReassembly) {
                    final SGRP_VAL_CHILD_DTO.Test testValDto = subgroupDataDTO.getTestValDto();
                    final List<SGRP_VAL_CHILD_DTO.SubTest> subTestList = testValDto.getSubTestList();
                    if (CollectionUtils.isEmpty(subTestList)) {
                        StreamTrendInfCatchDTO streamTrendInfCatch = new StreamTrendInfCatchDTO();
                        if (CollectionUtils.isNotEmpty(offsetIdList) && offsetIdList.contains(subgroupDataDTO.getF_SGRP())) {
                            streamTrendInfCatch.setF_CPK("/");
                        } else {
                            streamTrendInfCatch.setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                    BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue() + "");
                        }
                        streamTrendInfCatch
                                .setF_CP(dataSummaryDTO.getCp() == null ? null :
                                        BigDecimal.valueOf(dataSummaryDTO.getCp()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_TAR(dataSummaryDTO.getTargetValue() == null ? null :
                                        BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_CPTAR(specInfDto == null ? null : specInfDto.getF_CP() == null ? null :
                                        BigDecimal.valueOf(specInfDto.getF_CP()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_CPKTAR(specInfDto == null ? null : specInfDto.getF_CPK() == null ? null :
                                        BigDecimal.valueOf(specInfDto.getF_CPK()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                .setF_RANGE_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getRange() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getRange).sum())
                                .setF_SD_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getSd() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getSd).sum())
                                .setF_VALUE_COUNT(dataSummaryDTO.getSubGroupNum() * dataSummaryDTO.getSubGroupSize().intValue())
                                .setF_SGRP(subgroupDataDTO.getF_SGRP())
                                .setPartName(subgroupDataDTO.getPartName())
                                .setRevName(subgroupDataDTO.getPtrvName())
                                .setPrcsName(subgroupDataDTO.getPrcsName())
                                .setTestName(subgroupDataDTO.getTestName())
                                .setShiftName(subgroupDataDTO.getShiftName())
                                .setLotName(subgroupDataDTO.getLotName())
                                .setJobName(subgroupDataDTO.getJobName())
                                .setSgrpDscList(subgroupDataDTO.getSgrpDscList())
                                .setF_SGTM(DateUtil.offsetHour(subgroupDataDTO.getF_SGTM(), 8))
                                .setTestVal(testValDto.getTestVal())
                                .setTestNo(testValDto.getTestNo() + 1);

                        final String key = streamTrendInfCatch.getF_SGRP() + "_" + streamTrendInfCatch.getTestNo();
                        if (!listMap.containsKey(key)) {
                            catchList.add(streamTrendInfCatch);
                            listMap.put(key, "1");
                        }
                    } else {
                        for (SGRP_VAL_CHILD_DTO.SubTest subTest : subTestList) {
                            StreamTrendInfCatchDTO streamTrendInfCatch = new StreamTrendInfCatchDTO();
                            if (CollectionUtils.isNotEmpty(offsetIdList) && offsetIdList.contains(subgroupDataDTO.getF_SGRP())) {
                                streamTrendInfCatch.setF_CPK("/");
                            } else {
                                streamTrendInfCatch.setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                        BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue() + "");
                            }
                            streamTrendInfCatch
                                    .setF_CP(dataSummaryDTO.getCp() == null ? null :
                                            BigDecimal.valueOf(dataSummaryDTO.getCp()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_CPK(dataSummaryDTO.getCpk() == null ? null :
                                            BigDecimal.valueOf(dataSummaryDTO.getCpk()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue() + "")
                                    .setF_TAR(dataSummaryDTO.getTargetValue() == null ? null :
                                            BigDecimal.valueOf(dataSummaryDTO.getTargetValue()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_CPTAR(specInfDto == null ? null : specInfDto.getF_CP() == null ? null :
                                            BigDecimal.valueOf(specInfDto.getF_CP()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_CPKTAR(specInfDto == null ? null : specInfDto.getF_CPK() == null ? null :
                                            BigDecimal.valueOf(specInfDto.getF_CPK()).setScale(analysisChartConfigDTO.getReportDataPrecision(), RoundingMode.DOWN).doubleValue())
                                    .setF_RANGE_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getRange() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getRange).sum())
                                    .setF_SD_SUM(reassemblySubgroupDataDTOList.stream().map(SubgroupDataDTO::getSgrpValDto).map(SGRP_VAL_DTO::getSgrpValChildDto).filter(s -> s.getSd() != null).mapToDouble(SGRP_VAL_CHILD_DTO::getSd).sum())
                                    .setF_VALUE_COUNT(dataSummaryDTO.getSubGroupNum() * dataSummaryDTO.getSubGroupSize().intValue())
                                    .setF_SGRP(subgroupDataDTO.getF_SGRP())
                                    .setPartName(subgroupDataDTO.getPartName())
                                    .setRevName(subgroupDataDTO.getPtrvName())
                                    .setPrcsName(subgroupDataDTO.getPrcsName())
                                    .setTestName(subgroupDataDTO.getTestName())
                                    .setShiftName(subgroupDataDTO.getShiftName())
                                    .setLotName(subgroupDataDTO.getLotName())
                                    .setJobName(subgroupDataDTO.getJobName())
                                    .setSgrpDscList(subgroupDataDTO.getSgrpDscList())
                                    .setF_SGTM(DateUtil.offsetHour(subgroupDataDTO.getF_SGTM(), 8))
                                    .setTestVal(subTest.getSubTestValue())
                                    .setSubTestNo(subTest.getSubTestNo() + 1)
                                    .setTestNo(testValDto.getTestNo() + 1);
                            final String key = streamTrendInfCatch.getF_SGRP() + "_" + streamTrendInfCatch.getTestNo();
                            if (!listMap.containsKey(key)) {
                                catchList.add(streamTrendInfCatch);
                                listMap.put(key, "1");
                            }
                        }
                    }
                }
                size++;
            }
        }

        //如果仅一个数据点，且数据点对应的参数值为空时，直接显示无数据
        if(infoList.size() == 1){
            //cp
            if(realTimeCapabilityTrendConfigDTO.getShowType() == 1 && ObjectUtils.isEmpty(infoList.get(0).getF_CP())){
                infoList = Collections.emptyList();
            }
            //cpk
            if(realTimeCapabilityTrendConfigDTO.getShowType() == 2 && ObjectUtils.isEmpty(infoList.get(0).getF_CPK())){
                infoList = Collections.emptyList();
            }
        }
        if(CollectionUtils.isNotEmpty(infoList)) {
            resultMap.put("infoList", infoList);
            final StreamTrendInfCatchData2DTO streamTrendInfCatchData2Dto = new StreamTrendInfCatchData2DTO();
            if (realTimeCapabilityTrendConfigDTO.getGroupType() == 2 && realTimeCapabilityTrendConfigDTO.getOffset() > 0) {
                catchList.sort(
                        Comparator.comparing(StreamTrendInfCatchDTO::getF_SGTM).thenComparing(StreamTrendInfCatchDTO::getF_SGRP)
                );
            }

            HashMap<String, Object> stringObjectHashMap = new HashMap<>(3);
            stringObjectHashMap.put("partName", partNameSet.stream().collect(Collectors.joining(",")));
            stringObjectHashMap.put("prcsName", prcsNameSet.stream().collect(Collectors.joining(",")));
            stringObjectHashMap.put("testName", testNameSet.stream().collect(Collectors.joining(",")));
            resultMap.put("infoCondition", stringObjectHashMap);

            streamTrendInfCatchData2Dto.setData(catchList);
            streamTrendInfCatchData2Dto.setGroupByData(groupByList);
            resultMap.put("catchList", streamTrendInfCatchData2Dto);
        }
        return resultMap;
    }

    private static List<List<SubgroupDataDTO>> partitionWithOffset(List<SubgroupDataDTO> dataList, int groupSize, int offset) {
        List<List<SubgroupDataDTO>> result = new ArrayList<>();

        // 按偏移量和分组长度生成组
        for (int i = 0; i + groupSize <= dataList.size(); i += offset) {
            List<SubgroupDataDTO> group = new ArrayList<>(dataList.subList(i, i + groupSize));
            result.add(group);
        }
        return result;
    }


    /**
     * 导出实时能力趋势图数据
     *
     * @param parameterId
     * @param file
     * @param response
     */
    @Override
    /**
     * 导出实时能力趋势图数据
     * @param parameterId
     * @param type
     * @param file
     * @param response
     */
    public void exportStreamTrend(String parameterId, Integer type, MultipartFile file, HttpServletResponse response) throws IOException {

        String key = String.format(RedisConstant.STREAM_TREND_DATA, parameterId);
        /*获取聚合分析子组数据*/
        List<StreamTrendInfCatchData1DTO> streamTrendInfList = redisService.getCacheList(key);
        if (CollectionUtils.isEmpty(streamTrendInfList)) {
            return;
        }
        String key2 = String.format(RedisConstant.STREAM_TREND_DATA2, parameterId);
        /*获取聚合分析子组数据*/
        List<StreamTrendInfCatchData2DTO> streamTrendInfList2 = redisService.getCacheList(key2);
        if (CollectionUtils.isEmpty(streamTrendInfList2)) {
            return;
        }

        String conditionKey = String.format(RedisConstant.STREAM_TREND_CONDITION, parameterId);
        Map<String, String> map = redisService.getCacheObject(conditionKey);
        if (ObjectUtils.isEmpty(type) || Integer.valueOf(1).equals(type)) {
            //表头信息
            List<ExcelFillIn> list = new ArrayList<>();
            ExcelFillIn excelFillIn = new ExcelFillIn();
            excelFillIn.setContent("").setColumnStart(0).setColumnEnd(0).setLineStart(0).setLineEnd(0);
            list.add(excelFillIn);
            ExcelFillIn excelFillIn1 = new ExcelFillIn();
            excelFillIn1.setContent("实时能力分析").setColumnStart(1).setColumnEnd(3).setLineStart(0).setLineEnd(0).setMerge(true);
            list.add(excelFillIn1);

            int row = 1;
            for (String s : map.keySet()) {
                ExcelFillIn excelFillIn2 = new ExcelFillIn();
                excelFillIn2.setContent(s).setColumnStart(0).setColumnEnd(0).setLineStart(row).setLineEnd(row);
                ExcelFillIn excelFillIn3 = new ExcelFillIn();
                excelFillIn3.setContent(map.get(s)).setColumnStart(1).setColumnEnd(3).setLineStart(row).setLineEnd(row).setMerge(true);
                list.add(excelFillIn2);
                list.add(excelFillIn3);
                row++;
            }
            Map<Integer, List<ExcelFillIn>> collect = list.stream().collect(Collectors.groupingBy(ExcelFillIn::getLineStart));

            ExcelUtil<STREAM_TREND_INF_DTO> excelUtil = new ExcelUtil<>(STREAM_TREND_INF_DTO.class);
            if (streamTrendInfList.size() == 1) {
                excelUtil.exportFileExcel(response, streamTrendInfList.get(0).getInfoList(), "实时能力趋势图导出", "", Collections.singletonList(file), collect);
            } else {
                List<List<STREAM_TREND_INF_DTO>> paramList = new ArrayList<>();
                List<String> tableNames = new ArrayList<>();
                for (StreamTrendInfCatchData1DTO streamTrendInfCatchData1DTO : streamTrendInfList) {
                    paramList.add(streamTrendInfCatchData1DTO.getInfoList());
                    tableNames.add("产品：" + streamTrendInfCatchData1DTO.getPartName() + " 过程：" + streamTrendInfCatchData1DTO.getPrcsName() + " 测试：" + streamTrendInfCatchData1DTO.getTestName());
                }
                excelUtil.exportMultipleSameHeaderInOneSheet(response, paramList, tableNames, "实时能力趋势图导出", null, Collections.singletonList(file), collect);
            }
        } else {
            byte[] bytes = file == null ? null : ImageUtils.getImage(file);
            ExportStreamTrendUtil.exportExcel(response, streamTrendInfList2, file.getName(), map, bytes);
        }
    }


}

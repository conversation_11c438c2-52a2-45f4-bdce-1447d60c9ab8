package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface SingleAnalysisService {

    Map<String, Object> viewData(SubgroupDataSelectionDTO subgroupDataSelectionDTO);
    Map<String, Object> viewDataList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    Map<String, Object>  exceptionSummary(SubgroupDataSelectionDTO subgroupDataSelectionDTO);
    Map<String, Object>  exceptionSummaryList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    long getTotal(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    SubgroupDataSelectionDTO getSearch(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    List<STREAM_TREND_INF_DTO> realTimeCapabilityTrend(SubgroupDataSelectionDTO subgroupDataSelectionDTO);
    List<List<STREAM_TREND_INF_DTO>> realTimeCapabilityTrendList(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    /**
     * 导出实时能力趋势图数据
     * @param parameterId
     * @param type
     * @param file
     * @param response
     */
    void exportStreamTrend(String parameterId,Integer type, MultipartFile file, HttpServletResponse response) throws IOException;
}

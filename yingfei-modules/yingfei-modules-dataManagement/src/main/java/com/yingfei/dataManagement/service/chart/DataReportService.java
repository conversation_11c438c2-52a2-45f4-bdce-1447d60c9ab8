package com.yingfei.dataManagement.service.chart;

import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.DataReportDTO;

import java.util.List;
import java.util.Map;

public interface DataReportService {
    Map<String, Object> getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO);

    void structureSubgroupDataDTOList(List<SubgroupDataDTO> subgroupDataDTOList);
}

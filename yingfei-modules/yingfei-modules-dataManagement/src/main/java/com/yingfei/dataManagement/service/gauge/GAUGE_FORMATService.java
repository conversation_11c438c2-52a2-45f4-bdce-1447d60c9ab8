package com.yingfei.dataManagement.service.gauge;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.GAUGE_FORMAT;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.GAUGE_FORMAT_DTO;
import com.yingfei.entity.vo.GAUGE_FORMAT_VO;

/**
* 
* @description 针对表【GAUGE_FORMAT(量具解析规则配置表)】的数据库操作Service
* @createDate 2024-07-30 11:19:16
*/
public interface GAUGE_FORMATService extends IService<GAUGE_FORMAT>, BaseService<GAUGE_FORMAT_VO, GAUGE_FORMAT_DTO> {

    GAUGE_FORMAT_DTO info(Long id);
}

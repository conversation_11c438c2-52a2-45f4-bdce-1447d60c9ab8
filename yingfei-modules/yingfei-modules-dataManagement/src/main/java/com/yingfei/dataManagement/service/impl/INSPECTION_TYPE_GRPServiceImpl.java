package com.yingfei.dataManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.INSPECTION_TYPE_GRPMapper;
import com.yingfei.dataManagement.service.INSPECTION_TYPE_DATService;
import com.yingfei.dataManagement.service.INSPECTION_TYPE_GRPService;
import com.yingfei.entity.domain.INSPECTION_TYPE_DAT;
import com.yingfei.entity.domain.INSPECTION_TYPE_GRP;
import com.yingfei.entity.dto.INSPECTION_TYPE_GRP_DTO;
import com.yingfei.entity.vo.INSPECTION_TYPE_GRP_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class INSPECTION_TYPE_GRPServiceImpl extends ServiceImpl<INSPECTION_TYPE_GRPMapper, INSPECTION_TYPE_GRP> implements INSPECTION_TYPE_GRPService {

    @Resource
    private INSPECTION_TYPE_DATService inspectionTypeDatService;

    @Override
    public long getTotal(INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        return baseMapper.getTotal(inspectionTypeGrpVo);
    }

    @Override
    public List<INSPECTION_TYPE_GRP_DTO> getList(INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        return baseMapper.getList(inspectionTypeGrpVo);
    }

    @Override
    public void add(INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        INSPECTION_TYPE_GRP inspectionTypeGrp = new INSPECTION_TYPE_GRP();
        BeanUtils.copyPropertiesIgnoreNull(inspectionTypeGrpVo, inspectionTypeGrp);
        baseMapper.insert(inspectionTypeGrp);
    }

    @Override
    public void del(List<Long> ids) {
        /*判断是否有检验类型存在*/
        LambdaQueryWrapper<INSPECTION_TYPE_DAT> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(INSPECTION_TYPE_DAT::getF_INGP,ids);
        List<INSPECTION_TYPE_DAT> list = inspectionTypeDatService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)){
            throw new BusinessException(DataManagementExceptionEnum.INSPECTION_TYPE_DAT_DUPLICATION_EXCEPTION);
        }
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public void checkParam(INSPECTION_TYPE_GRP_VO inspectionTypeGrpVo) {
        LambdaQueryWrapper<INSPECTION_TYPE_GRP> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(INSPECTION_TYPE_GRP::getF_NAME, inspectionTypeGrpVo.getF_NAME()).eq(INSPECTION_TYPE_GRP::getF_DEL, DelFlagEnum.USE.getType());
        List<INSPECTION_TYPE_GRP> inspectionTypeGrpList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(inspectionTypeGrpList)) return;
        if (inspectionTypeGrpVo.getF_ID() != null) {
            if (inspectionTypeGrpList.size() > 1 || !Objects.equals(inspectionTypeGrpList.get(0).getF_ID(), inspectionTypeGrpVo.getF_ID())) {
                throw new BusinessException(DataManagementExceptionEnum.INSPECTION_TYPE_GRP_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (inspectionTypeGrpList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.INSPECTION_TYPE_GRP_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }
}

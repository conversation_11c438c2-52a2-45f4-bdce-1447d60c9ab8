package com.yingfei.dataManagement.service.gauge.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.service.gauge.GAUGE_CONNECTIONService;
import com.yingfei.dataManagement.service.gauge.GAUGE_INTERFACEService;
import com.yingfei.entity.domain.GAUGE_CONNECTION;
import com.yingfei.entity.domain.GAUGE_INTERFACE;
import com.yingfei.dataManagement.mapper.GAUGE_INTERFACEMapper;
import com.yingfei.entity.dto.GAUGE_INTERFACE_DTO;
import com.yingfei.entity.vo.GAUGE_INTERFACE_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description 针对表【GAUGE_INTERFACE(量具接口配置表)】的数据库操作Service实现
 * @createDate 2024-07-30 11:19:20
 */
@Service
public class GAUGE_INTERFACEServiceImpl extends ServiceImpl<GAUGE_INTERFACEMapper, GAUGE_INTERFACE>
        implements GAUGE_INTERFACEService {

    @Resource
    private GAUGE_CONNECTIONService gaugeConnectionService;

    @Override
    public long getTotal(GAUGE_INTERFACE_VO gaugeInterfaceVo) {
        return 0;
    }

    @Override
    public List<GAUGE_INTERFACE_DTO> getList(GAUGE_INTERFACE_VO gaugeInterfaceVo) {
        return baseMapper.getList(gaugeInterfaceVo);
    }

    @Override
    public void add(GAUGE_INTERFACE_VO gaugeInterfaceVo) {
        GAUGE_INTERFACE gaugeInterface = new GAUGE_INTERFACE();
        BeanUtils.copyPropertiesIgnoreNull(gaugeInterfaceVo, gaugeInterface);
        baseMapper.insert(gaugeInterface);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
        /*删除对应的量具连接数据*/
        LambdaQueryWrapper<GAUGE_CONNECTION> gaugeConnectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gaugeConnectionLambdaQueryWrapper.in(GAUGE_CONNECTION::getF_GAIN, ids);
        gaugeConnectionService.remove(gaugeConnectionLambdaQueryWrapper);
    }

    @Override
    public void checkParam(GAUGE_INTERFACE_VO gaugeInterfaceVo) {
        if (StringUtils.isEmpty(gaugeInterfaceVo.getF_NAME())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        LambdaQueryWrapper<GAUGE_INTERFACE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GAUGE_INTERFACE::getF_NAME, gaugeInterfaceVo.getF_NAME());
        List<GAUGE_INTERFACE> gaugeInterfaceList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(gaugeInterfaceList)) return;
        if (StringUtils.isNotEmpty(gaugeInterfaceVo.getF_GAIN())) {
            if (gaugeInterfaceList.size() > 1 || !Objects.equals(gaugeInterfaceList.get(0).getF_GAIN(), gaugeInterfaceVo.getF_GAIN())) {
                throw new BusinessException(DataManagementExceptionEnum.GAUGE_INTERFACE_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (gaugeInterfaceList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.GAUGE_INTERFACE_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }
}





package com.yingfei.dataManagement.service.chart.impl;

import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.LinearRegressionService;
import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.LinearRegressionDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.enums.TEST_INF_TYPEEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LinearRegressionServiceImpl implements LinearRegressionService {

    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private ChartCommonService chartCommonService;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private RedisService redisService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    public LinearRegressionDTO getInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.LINEAR_REGRESSION);

        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }


        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        LinearRegressionDTO linearRegressionDTO = new LinearRegressionDTO();
        if (subgroupDataSelectionDTO == null)
            return linearRegressionDTO;

        if (analysisChartConfigDTO.getIsTopOne() == 0) {
            if (subgroupDataSelectionDTO.getF_PART() == null || subgroupDataSelectionDTO.getF_TEST() == null || subgroupDataSelectionDTO.getF_PRCS() == null)
                return linearRegressionDTO;
        }
        /*线性回归只查询测试类型为变量的*/
        subgroupDataSelectionDTO.setTestType(YesOrNoEnum.YES.getType());
        /*获取子组测试数量*/
        List<SubgroupDataDTO> sgrpExtList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(sgrpExtList)) {
            return linearRegressionDTO;
        }

        return getLinearRegressionDTO(sgrpExtList, subgroupDataSelectionDTO.getTestType(), analysisChartConfigDTO, linearRegressionDTO, subgroupDataSelectionDTO);
    }

    private LinearRegressionDTO getLinearRegressionDTO(List<SubgroupDataDTO> sgrpExtList, Integer testType, AnalysisChartConfigDTO analysisChartConfigDTO, LinearRegressionDTO linearRegressionDTO,SubgroupDataSelectionDTO subgroupDataSelectionDTO) {

        sgrpExtList = chartCommonService.getTotalNumSubgroup(sgrpExtList, subgroupDataSelectionDTO.getMaxNum(), 2);
        /*通过测试数据重新组装子组*/
        List<SubgroupDataDTO> subgroupDataDTOArrayList = chartCommonService.reassembly(sgrpExtList);
        if(CollectionUtils.isEmpty(subgroupDataDTOArrayList)){
            return linearRegressionDTO;
        }
        if (testType == YesOrNoEnum.YES.getType()) {
            subgroupDataDTOArrayList = subgroupDataDTOArrayList.stream().filter(s -> s.getTestType() == TEST_INF_TYPEEnum.VARIABLE.getType()).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(subgroupDataDTOArrayList)){
            return linearRegressionDTO;
        }
        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(subgroupDataDTOArrayList, analysisChartConfigDTO.getType(), 0);
        /*对scrpExtList的F_VAL字段做升序排列*/
        List<Double> valList = dataSummaryDTO.getValList().stream().sorted(Comparator.comparing(Double::doubleValue)).collect(Collectors.toList());
        int size = valList.size();
        List<Double> normalQuantileList = new ArrayList<>();
        for (int i = 1; i <= size; i++) {
            Double p = BigDecimal.valueOf(i).subtract(BigDecimal.valueOf(0.3))
                    .divide(BigDecimal.valueOf(size).add(BigDecimal.valueOf(0.4)), 10, RoundingMode.HALF_UP)
                    .doubleValue();
            normalQuantileList.add(p);
        }
        linearRegressionDTO.setXValueList(valList);
        linearRegressionDTO.setScatterList(normalQuantileList);
        linearRegressionDTO.setDataSummary(dataSummaryDTO);
        linearRegressionDTO.setSubgroupDataDTO(subgroupDataDTOArrayList.get(0));
        return linearRegressionDTO;
    }

    @Override
    public List<LinearRegressionDTO> getInfoList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.LINEAR_REGRESSION);


        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }

        List<SubgroupDataDTO> sgrpExtList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (CollectionUtils.isEmpty(sgrpExtList)) {
            return Collections.emptyList();
        }
        /*多测试拆分*/
        sgrpExtList = chartCommonService.reassembly(sgrpExtList);

        final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                subgroupDataSelectionDTO.getPartList(),
                subgroupDataSelectionDTO.getPtrvList(),
                subgroupDataSelectionDTO.getPrcsList(),
                subgroupDataSelectionDTO.getTestList());
        List<LinearRegressionDTO> list = new ArrayList<>();

        List<Future<LinearRegressionDTO>> futures = new ArrayList<>();

        for (Map<String, Long> map : queryMapList) {
            // 进行空值检查
            if (map == null) {
                continue;
            }
            Long fpart = map.get(Constants.part);
            Long fptrv = map.get(Constants.ptrv);
            Long fprcs = map.get(Constants.prcs);
            Long ftest = map.get(Constants.test);
            // 过滤符合条件的 SubgroupDataDTO 列表
            final List<SubgroupDataDTO> collect = sgrpExtList.stream()
                    .filter(x -> fpart != null && fpart.equals(x.getF_PART()) &&
                            fptrv != null && fptrv.equals(x.getF_REV()) &&
                            fprcs != null && fprcs.equals(x.getF_PRCS()) &&
                            ftest != null && ftest.equals(x.getF_TEST()))
                    .collect(Collectors.toList());

            // 当 collect 列表为空时，跳过当前循环
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }
            subgroupDataSelectionDTO.setF_PART(fpart).setF_REV(fptrv).setF_PRCS(fprcs).setF_TEST(ftest);
            // 提交任务到线程池
            futures.add(threadPoolConfig.threadPoolTaskExecutor().submit(() -> getLinearRegressionDTO(collect, subgroupDataSelectionDTO.getTestType(), analysisChartConfigDTO, new LinearRegressionDTO(),subgroupDataSelectionDTO)));
        }
        // 获取任务结果
        for (Future<LinearRegressionDTO> future : futures) {
            try {
                LinearRegressionDTO dto = future.get();
                if (dto != null) {
                    list.add(dto);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程处理出错", e);
            }
        }
        return list;
    }


}

package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.ANALYSIS_DASHBOARD_TEMPLATE_INFMapper;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_INF;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_TEMPLATE_INF;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO;
import com.yingfei.entity.dto.MENU_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.vo.ANALYSIS_DASHBOARD_TEMPLATE_INF_VO;
import com.yingfei.entity.vo.MENU_INF_VO;
import com.yingfei.system.api.RemoteMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description 针对表【ANALYSIS_DASHBOARD_TEMPLATE_INF(分析页面模板表)】的数据库操作Service实现
 * @createDate 2024-09-04 16:01:18
 */
@Service
public class ANALYSIS_DASHBOARD_TEMPLATE_INFServiceImpl extends ServiceImpl<ANALYSIS_DASHBOARD_TEMPLATE_INFMapper, ANALYSIS_DASHBOARD_TEMPLATE_INF>
        implements ANALYSIS_DASHBOARD_TEMPLATE_INFService {

    @Resource
    private ANALYSIS_DASHBOARD_INFService analysisDashboardInfService;
    @Resource
    private RemoteMenuService remoteMenuService;

    @Override
    public long getTotal(ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        return baseMapper.getTotal(analysisDashboardTemplateInfVo);
    }

    @Override
    public List<ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO> getList(ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        List<ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO> list = baseMapper.getList(analysisDashboardTemplateInfVo);
        list.forEach(analysisDashboardTemplateInfDto -> {
            LambdaQueryWrapper<ANALYSIS_DASHBOARD_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ANALYSIS_DASHBOARD_INF::getF_ADTI, analysisDashboardTemplateInfDto.getF_ADTI());
            List<ANALYSIS_DASHBOARD_INF> analysisDashboardInfList = analysisDashboardInfService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(analysisDashboardInfList)) {
                analysisDashboardTemplateInfDto.setMenuSize(analysisDashboardInfList.size());
                Set<Long> collect = analysisDashboardInfList.stream().map(ANALYSIS_DASHBOARD_INF::getF_MENU).collect(Collectors.toSet());
                MENU_INF_VO menuInfVo = new MENU_INF_VO();
                menuInfVo.setDbType(InitConfig.getDriverType());
                menuInfVo.setMenuIds(new ArrayList<>(collect));
                R<List<MENU_INF_DTO>> listR = remoteMenuService.searchList(menuInfVo);
                if (listR.getData() != null) {
                    analysisDashboardTemplateInfDto.setMenuInfDtoList(listR.getData());
                }
            }
        });
        return list;
    }

    @Override
    public void add(ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = new ANALYSIS_DASHBOARD_TEMPLATE_INF();
        BeanUtils.copyPropertiesIgnoreNull(analysisDashboardTemplateInfVo, analysisDashboardTemplateInf);
        if (CollectionUtils.isNotEmpty(analysisDashboardTemplateInfVo.getAnalysisChartConfigDTOList())) {
            String jsonString = JSONArray.toJSONString(analysisDashboardTemplateInfVo.getAnalysisChartConfigDTOList());
            analysisDashboardTemplateInf.setF_DATA(jsonString);
        }
        baseMapper.insert(analysisDashboardTemplateInf);
    }

    @Override
    public void del(List<Long> ids) {
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public void checkParam(ANALYSIS_DASHBOARD_TEMPLATE_INF_VO analysisDashboardTemplateInfVo) {
        if (StringUtils.isEmpty(analysisDashboardTemplateInfVo.getF_NAME())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        LambdaQueryWrapper<ANALYSIS_DASHBOARD_TEMPLATE_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ANALYSIS_DASHBOARD_TEMPLATE_INF::getF_NAME, analysisDashboardTemplateInfVo.getF_NAME());
        List<ANALYSIS_DASHBOARD_TEMPLATE_INF> analysisDashboardTemplateInfList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(analysisDashboardTemplateInfList)) return;
        if (StringUtils.isNotEmpty(analysisDashboardTemplateInfVo.getF_ADTI())) {
            if (analysisDashboardTemplateInfList.size() > 1 || !Objects.equals(analysisDashboardTemplateInfList.get(0).getF_ADTI(), analysisDashboardTemplateInfVo.getF_ADTI())) {
                throw new BusinessException(DataManagementExceptionEnum.ANALYSIS_DASHBOARD_TEMPLATE_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (!analysisDashboardTemplateInfList.isEmpty()) {
                throw new BusinessException(DataManagementExceptionEnum.ANALYSIS_DASHBOARD_TEMPLATE_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }

    @Override
    public AnalysisChartConfigDTO findByAnalysisChartConfig(SubgroupDataSelectionDTO subgroupDataSelectionDTO, ChartTypeEnum chartTypeEnum) {
        if (StringUtils.isEmpty(subgroupDataSelectionDTO.getChartId())) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_CONFIG_NOT_EXISTS);
        }
        /*根据菜单获取分析模板配置*/
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(subgroupDataSelectionDTO.getMenuId());

        /*直方图,控制图...选择的测试id判断*/
//        if (StringUtils.isNotEmpty(analysisDashboardInf.getF_DATA())){
//            List<AnalysisDashboardConfigDTO> analysisDashboardConfigDTOList = JSONArray.parseArray(analysisDashboardInf.getF_DATA(), AnalysisDashboardConfigDTO.class);
//            for (AnalysisDashboardConfigDTO analysisDashboardConfigDTO : analysisDashboardConfigDTOList) {
//                if (analysisDashboardConfigDTO.getChartId().equals(subgroupDataSelectionDTO.getChartId())) {
//                    if (StringUtils.isEmpty(subgroupDataSelectionDTO.getF_TEST())){
//                        subgroupDataSelectionDTO.setF_TEST(analysisDashboardConfigDTO.getTestId());
//                        break;
//                    }
//                }
//            }
//        }
        if (StringUtils.isEmpty(subgroupDataSelectionDTO.getParameterId())) {
            subgroupDataSelectionDTO.setParameterId(analysisDashboardInf.getF_PRST());
        }
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = baseMapper.selectById(analysisDashboardInf.getF_ADTI());

        List<AnalysisChartConfigDTO> analysisChartConfigDTOS = JSONArray.parseArray(analysisDashboardTemplateInf.getF_DATA(), AnalysisChartConfigDTO.class);
        AnalysisChartConfigDTO analysisChartConfigDTO = AnalysisChartConfigDTO.getAnalysisChartConfigDTO(analysisChartConfigDTOS, subgroupDataSelectionDTO.getChartId());
        if (analysisChartConfigDTO != null
                && StringUtils.isNotEmpty(analysisDashboardTemplateInf.getF_CONFIG())
                && !Objects.equals(analysisDashboardTemplateInf.getF_CONFIG(), "null")
        ) {
            JSONObject jsonObject = JSONObject.parseObject(analysisDashboardTemplateInf.getF_CONFIG());
            if (ObjectUtils.isNotEmpty(jsonObject)  && ObjectUtils.isNotEmpty(jsonObject.get("maxNum"))) {
                analysisChartConfigDTO.setMaxNum(jsonObject.getInteger("maxNum"));
            }
        }
        return analysisChartConfigDTO;
    }

    @Override
    public void editConfig(Long menuId, String chartId, Object config, ChartTypeEnum chartTypeEnum) {
        /*根据菜单获取分析模板配置*/
        ANALYSIS_DASHBOARD_INF analysisDashboardInf = analysisDashboardInfService.findByMenuId(menuId);
        ANALYSIS_DASHBOARD_TEMPLATE_INF analysisDashboardTemplateInf = baseMapper.selectById(analysisDashboardInf.getF_ADTI());
        List<AnalysisChartConfigDTO> analysisChartConfigDTOS = JSONArray.parseArray(analysisDashboardTemplateInf.getF_DATA(), AnalysisChartConfigDTO.class);
        for (AnalysisChartConfigDTO analysisChartConfigDTO : analysisChartConfigDTOS) {
            if (analysisChartConfigDTO.getId().equals(chartId)) {
                analysisChartConfigDTO.setChartConfig(config);
                return;
            }
        }
        analysisDashboardTemplateInf.setF_DATA(JSONArray.toJSONString(analysisChartConfigDTOS));
        baseMapper.updateById(analysisDashboardTemplateInf);
    }
}





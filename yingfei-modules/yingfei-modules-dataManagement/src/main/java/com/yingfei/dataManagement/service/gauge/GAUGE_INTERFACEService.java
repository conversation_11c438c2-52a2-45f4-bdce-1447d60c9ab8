package com.yingfei.dataManagement.service.gauge;

import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.GAUGE_INTERFACE;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.GAUGE_INTERFACE_DTO;
import com.yingfei.entity.vo.GAUGE_INTERFACE_VO;

/**
* 
* @description 针对表【GAUGE_INTERFACE(量具接口配置表)】的数据库操作Service
* @createDate 2024-07-30 11:19:20
*/
public interface GAUGE_INTERFACEService extends IService<GAUGE_INTERFACE>, BaseService<GAUGE_INTERFACE_VO, GAUGE_INTERFACE_DTO> {

}

package com.yingfei.dataManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.dataManagement.mapper.RESPONSE_ACTION_DATMapper;
import com.yingfei.dataManagement.service.RESPONSE_ACTION_DATService;
import com.yingfei.entity.domain.PROCESSING_TEMPLATE_INF;
import com.yingfei.entity.domain.RESPONSE_ACTION_DAT;
import com.yingfei.entity.dto.RESPONSE_ACTION_DAT_DTO;
import com.yingfei.entity.vo.RESPONSE_ACTION_DAT_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【RESPONSE_ACTION_DAT(储存改善措施信息表)】的数据库操作Service实现
* @createDate 2024-05-08 16:28:17
*/
@Service
public class RESPONSE_ACTION_DATServiceImpl extends ServiceImpl<RESPONSE_ACTION_DATMapper, RESPONSE_ACTION_DAT>
    implements RESPONSE_ACTION_DATService {

    @Override
    public long getTotal(RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        return baseMapper.getTotal(responseActionDatVo);
    }

    @Override
    public List<RESPONSE_ACTION_DAT_DTO> getList(RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        return baseMapper.getList(responseActionDatVo);
    }

    @Override
    public void add(RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        RESPONSE_ACTION_DAT responseActionDat = new RESPONSE_ACTION_DAT();
        BeanUtils.copyPropertiesIgnoreNull(responseActionDatVo,responseActionDat);
        baseMapper.insert(responseActionDat);
    }

    @Override
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }

        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<RESPONSE_ACTION_DAT> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(RESPONSE_ACTION_DAT::getF_RSAT, ids)
                .set(RESPONSE_ACTION_DAT::getF_DEL, YesOrNoEnum.YES.getType());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void checkParam(RESPONSE_ACTION_DAT_VO responseActionDatVo) {
        if (StringUtils.isEmpty(responseActionDatVo.getF_NAME()) || StringUtils.isEmpty(responseActionDatVo.getF_RAGP())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        LambdaQueryWrapper<RESPONSE_ACTION_DAT> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RESPONSE_ACTION_DAT::getF_NAME, responseActionDatVo.getF_NAME())
                .eq(RESPONSE_ACTION_DAT::getF_RAGP,responseActionDatVo.getF_RAGP())
                .eq(RESPONSE_ACTION_DAT::getF_DEL, DelFlagEnum.USE.getType());
        List<RESPONSE_ACTION_DAT> responseActionDatList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(responseActionDatList)) return;
        if (StringUtils.isNotEmpty(responseActionDatVo.getF_RSAT())) {
            if (responseActionDatList.size() > 1 || !Objects.equals(responseActionDatList.get(0).getF_RSAT(), responseActionDatVo.getF_RSAT())) {
                throw new BusinessException(DataManagementExceptionEnum.RESPONSE_ACTION_NAME_DUPLICATION_EXCEPTION);
            }
        } else {
            if (responseActionDatList.size() > 0) {
                throw new BusinessException(DataManagementExceptionEnum.RESPONSE_ACTION_NAME_DUPLICATION_EXCEPTION);
            }
        }
    }
}





package com.yingfei.dataManagement.service.chart;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.ANALYSIS_DASHBOARD_TEMPLATE_INF;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO;
import com.yingfei.entity.dto.SubgroupDataSelectionDTO;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.vo.ANALYSIS_DASHBOARD_TEMPLATE_INF_VO;

/**
 * @description 针对表【ANALYSIS_DASHBOARD_TEMPLATE_INF(分析页面模板表)】的数据库操作Service
 * @createDate 2024-09-04 16:01:18
 */
public interface ANALYSIS_DASHBOARD_TEMPLATE_INFService extends IService<ANALYSIS_DASHBOARD_TEMPLATE_INF>, BaseService<ANALYSIS_DASHBOARD_TEMPLATE_INF_VO, ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO> {

    AnalysisChartConfigDTO findByAnalysisChartConfig(SubgroupDataSelectionDTO subgroupDataSelectionDTO, ChartTypeEnum chartTypeEnum);

    void editConfig(Long menuId, String chartId,Object boxPlotsConfigDTO, ChartTypeEnum chartTypeEnum);
}

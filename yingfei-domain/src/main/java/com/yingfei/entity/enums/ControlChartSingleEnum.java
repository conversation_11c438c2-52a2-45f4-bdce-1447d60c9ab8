package com.yingfei.entity.enums;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import lombok.Getter;

/**
 * 控制限计算（基于数据库中控制限记录）
 */

@Getter
public enum ControlChartSingleEnum {

    CHART_GROUP_TYPE_SD(1, "标准差图", "com.yingfei.entity.util.controlChart.ControlChartGroupSD", 32),

    CHART_GROUP_TYPE_SDW(2, "标准差件内图", "com.yingfei.entity.util.controlChart.ControlChartGroupSDW", 2),

    CHART_GROUP_TYPE_IX(3, "单值图", "com.yingfei.entity.util.controlChart.ControlChartGroupIX", 768),

    CHART_GROUP_TYPE_RANGE(4, "极差图", "com.yingfei.entity.util.controlChart.ControlChartGroupRange", 16),

    CHART_GROUP_TYPE_RW(5, "极差件内图", "com.yingfei.entity.util.controlChart.ControlChartGroupRW", 1),

    CHART_GROUP_TYPE_X(6, "均值图", "com.yingfei.entity.util.controlChart.ControlChartGroupX", 256),

    CHART_GROUP_TYPE_MR(7, "移动极差图", "com.yingfei.entity.util.controlChart.ControlChartGroupMR", 48),

    CHART_GROUP_TYPE_P(8, "P图", "com.yingfei.entity.util.controlChart.ControlChartGroupP", 3328),

    CHART_GROUP_TYPE_U(9, "U图", "com.yingfei.entity.util.controlChart.ControlChartGroupU", 3840),

    CHART_GROUP_TYPE_C(11, "C图", "com.yingfei.entity.util.controlChart.ControlChartGroupC", 3840),

    CHART_GROUP_TYPE_NP(12, "NP图", "com.yingfei.entity.util.controlChart.ControlChartGroupNP", 3840),

    CHART_GROUP_TYPE_LaneyU(13, "LaneyU图", "com.yingfei.entity.util.controlChart.ControlChartGroupLaneyU", 3840),

    CHART_GROUP_TYPE_LaneyP(14, "LaneyP图", "com.yingfei.entity.util.controlChart.ControlChartGroupLaneyP", 3840),
    ;

    /**
     * 图表分类
     */
    private Integer type;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 类路径
     */
    private String classPath;

    /**
     * 报警规则对应的图表类型
     * todo 后续如果没用删除
     */
    private Integer ruleType;


    ControlChartSingleEnum(Integer type, String description, String classPath, Integer ruleType) {
        this.type = type;
        this.description = description;
        this.classPath = classPath;
        this.ruleType = ruleType;
    }

    /**
     * 获取单个图表数据点
     * @param chartSingleEnum  图表类型
     * @param controlLimitDto  控制限及数据点所需数据
     * @param sgrpValChildDto  子组测试对象
     * @return
     */
    public static Double getDataPointVal(ControlChartSingleEnum chartSingleEnum, ControlLimitDTO controlLimitDto, SGRP_VAL_CHILD_DTO sgrpValChildDto){
        Double d = 0d;
        switch (chartSingleEnum){
            case CHART_GROUP_TYPE_SD:
                d = controlLimitDto.getNowSD();
                break;
            case CHART_GROUP_TYPE_SDW:
                d = sgrpValChildDto.getWithinPieceSdMax();
                break;
            case CHART_GROUP_TYPE_IX:
                d = controlLimitDto.getNowMean();
                break;
            case CHART_GROUP_TYPE_RANGE:
                d = controlLimitDto.getNowR();
                break;
            case CHART_GROUP_TYPE_RW:
                d = sgrpValChildDto.getWithinPieceRangeMax();
                break;
            case CHART_GROUP_TYPE_X:
                d = controlLimitDto.getNowMean();
                break;
            case CHART_GROUP_TYPE_MR:
                d = controlLimitDto.getNowMR();
                break;
        }
        return d;
    }
}

# yingfei-ADMIN

代码接口  
~~~
com.yingfei     
├── yingfei-gateway         // 网关模块 [8080](鉴权,过滤请求)  所有请求都通过网关进行转发
├── yingfei-auth            // 认证中心 [9200]  用户认证
├── yingfei-api             // Feign接口模块
│       └── yingfei-api-system                          // 系统接口
│       └── yingfei-api-dataManage                      // 数据管理接口
│       └── ...                                         // 对应业务模块
├── yingfei-common          // 通用模块
│       └── yingfei-common-core                         // 核心模块
│       └── yingfei-common-datascope                    // 权限范围
│       └── yingfei-common-datasource                   // 多数据源
│       └── yingfei-common-log                          // 日志记录
│       └── yingfei-common-redis                        // 缓存服务
│       └── yingfei-common-seata                        // 分布式事务
│       └── yingfei-common-security                     // 安全模块
│       └── yingfei-common-swagger                      // 系统接口
├── yingfei-domain          // 实体类模块
├── yingfei-modules         // 业务模块
│       └── yingfei-modules-system                      // 系统模块 [9210] 用户相关,角色相关,菜单相关
│       └── yingfei-modules-dataCollection              // 采集模块 [9220] 采集数据,数据处理
│       └── yingfei-modules-dataAnalysis                // 数据分析 [9230]  数据分析
│       └── yingfei-modules-dataManagement              // 数据管理,基础信息,数据查询模块 [9240]
│       └── yingfei-modules-dataMonitor                 // 数据监控模块 [9250]
├──pom.xml                // 公共依赖
~~~

## 🔧 特殊功能

### JSON 特殊字符转义处理

项目中实现了完善的 JSON 特殊字符转义解决方案，解决了 Jackson 序列化时特殊字符（如 `<`、`>`、`&`）导致的异常问题。

**核心组件**:
- `StringEscapeSerializer` - 自定义 Jackson 序列化器
- `JsonEscapeUtil` - 通用字符串转义工具类

**使用示例**:
```java
// 在 DTO 中使用自定义序列化器
@JsonSerialize(using = StringEscapeSerializer.class)
private List<List<Object>> contentList = new ArrayList<>();

// 使用工具类进行字符串转义
String escaped = JsonEscapeUtil.escapeOptimized("<LSL");
// 结果: "\u003cLSL"
```

**详细文档**: 参见 [JSON特殊字符转义解决方案](./Docs/JSON特殊字符转义解决方案.md)

## 🔧 Redis 连接优化

### Redis 超时问题解决方案

项目中实现了完善的 Redis 连接超时问题解决方案，解决了 `RedisResponseTimeoutException` 异常。

**核心组件**:
- `RedissonConfig` - 优化的 Redisson 配置类
- `RedisHealthMonitor` - Redis 连接健康监控
- `RedisRetryUtil` - 智能重试机制工具
- `EnhancedRedisService` - 增强版 Redis 服务

**主要优化**:
- **超时配置优化**: 连接超时从 10s 增加到 15s，命令超时从 3s 增加到 10s
- **重试机制增强**: 重试次数从 3 次增加到 5 次，支持指数退避策略
- **连接池优化**: 连接池大小增加到 50，最小空闲连接 10 个
- **线程配置优化**: Netty 线程数根据 CPU 核心数自动调整
- **健康监控**: 每 30 秒自动检查连接状态，连续失败 3 次发出警告

**详细文档**: 参见 [Redis超时问题解决方案](./Docs/Redis超时问题解决方案.md)

## 📋 更新日志

### 2025-08-07
- ✅ **新增**: Redis 连接超时问题解决方案
  - 实现 `RedissonConfig` 优化配置，解决 `RedisResponseTimeoutException`
  - 创建 `RedisHealthMonitor` 健康监控组件，定期检查连接状态
  - 开发 `RedisRetryUtil` 智能重试工具，支持指数退避和异常判断
  - 提供 `EnhancedRedisService` 增强服务，所有操作自带重试机制
  - 优化连接池、超时、线程配置，提升连接稳定性和性能
  - **参数配置化**: 所有硬编码参数抽取到配置文件，支持环境差异化配置

- ✅ **新增**: JSON 特殊字符转义功能
  - 实现 `StringEscapeSerializer` 自定义序列化器
  - 创建 `JsonEscapeUtil` 通用转义工具类
  - 解决 SPC 系统中 `<`、`>`、`&` 等特殊字符序列化异常问题
  - 支持多种转义模式：基础转义、完整转义、性能优化转义
  - 提供反转义和特殊字符检测功能

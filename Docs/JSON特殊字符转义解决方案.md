# JSON 特殊字符转义解决方案

## 问题描述

在 SPC 系统中，`contentList` 字段包含特殊字符（如 `<`、`>`、`&`）时，Jackson 序列化会出现异常。特别是在处理以下数据时：

- `">USL"`
- `"<LSL"`
- `"实际值>USL"`
- `"实际值<LSL"`
- `"预期值>USL"`
- `"预期值<LSL"`

## 解决方案

### 1. 自定义序列化器 `StringEscapeSerializer`

**位置**: `yingfei-common/yingfei-common-core/src/main/java/com/yingfei/common/core/config/StringEscapeSerializer.java`

**功能**: 专门处理 `List<List<Object>>` 类型数据的序列化，对字符串类型的元素进行特殊字符转义。

**使用方法**:
```java
@JsonSerialize(using = StringEscapeSerializer.class)
private List<List<Object>> contentList = new ArrayList<>();
```

**转义规则**:
- `<` → `\u003c`
- `>` → `\u003e`
- `&` → `\u0026`
- `"` → `\u0022`
- `'` → `\u0027`
- 控制字符（`\n`、`\t`、`\r` 等）→ 对应的 Unicode 转义

### 2. 通用工具类 `JsonEscapeUtil`

**位置**: `yingfei-common/yingfei-common-core/src/main/java/com/yingfei/common/core/utils/JsonEscapeUtil.java`

**功能**: 提供多种字符串转义方法，适用于不同场景。

**主要方法**:

#### 基础转义
```java
String escaped = JsonEscapeUtil.escapeBasic("<LSL");
// 结果: "\u003cLSL"
```

#### 完整转义（包含控制字符）
```java
String escaped = JsonEscapeUtil.escapeFull("line\nbreak");
// 结果: "line\u000abreak"
```

#### 性能优化版本
```java
String escaped = JsonEscapeUtil.escapeOptimized("normal text");
// 如果不包含特殊字符，直接返回原字符串对象
```

#### 反转义
```java
String unescaped = JsonEscapeUtil.unescape("\u003cLSL");
// 结果: "<LSL"
```

#### 特殊字符检测
```java
boolean hasSpecial = JsonEscapeUtil.containsSpecialCharacters("<LSL");
// 结果: true
```

#### 批量转义
```java
String[] escaped = JsonEscapeUtil.escapeArray(new String[]{"<LSL", ">USL"});
// 结果: ["\u003cLSL", "\u003eUSL"]
```

## 实际应用示例

### 在 ExportImgDTO 中的使用

```java
@Data
public class ExportImgDTO {
    // ... 其他字段
    
    /**
     * 列表内容 - 使用自定义序列化器处理特殊字符
     */
    @JsonSerialize(using = StringEscapeSerializer.class)
    private List<List<Object>> contentList = new ArrayList<>();
}
```

### 处理 SPC 系统实际数据

```java
// 原始数据
List<List<Object>> contentList = Arrays.asList(
    Arrays.asList(">USL", null, 3, 0.75),
    Arrays.asList("<LSL", null, 1, 0.25),
    Arrays.asList("实际值>USL", "实际值<LSL", "实际值总数", "实际PPM")
);

// 序列化后的 JSON（特殊字符已转义）
// [
//   ["\u003eUSL", null, 3, 0.75],
//   ["\u003cLSL", null, 1, 0.25],
//   ["实际值\u003eUSL", "实际值\u003cLSL", "实际值总数", "实际PPM"]
// ]
```

## 性能特点

### StringEscapeSerializer
- **优势**: 专门针对二维列表优化，处理效率高
- **适用场景**: `contentList` 字段的序列化

### JsonEscapeUtil
- **escapeBasic()**: 只处理常见特殊字符，性能最佳
- **escapeFull()**: 处理所有可能的特殊字符，功能最全
- **escapeOptimized()**: 智能检测，无特殊字符时直接返回原字符串

## 测试验证

可以使用 `JsonEscapeTestMain` 类进行功能验证：

```java
// 运行测试
java com.yingfei.common.core.utils.JsonEscapeTestMain
```

## 注意事项

1. **序列化后的数据**: 转义后的字符串在 JSON 中是安全的，但显示时需要考虑是否需要反转义
2. **性能考虑**: 对于大量数据，建议使用 `escapeOptimized()` 方法
3. **兼容性**: 转义后的 JSON 可以被标准的 JSON 解析器正确处理
4. **扩展性**: 可以根据需要在 `ESCAPE_MAP` 中添加更多特殊字符的处理

## 相关文件

- `StringEscapeSerializer.java` - 自定义序列化器
- `JsonEscapeUtil.java` - 通用转义工具类
- `ExportImgDTO.java` - 使用示例
- `JsonEscapeTestMain.java` - 功能测试类

## 总结

通过这套解决方案，可以有效解决 Jackson 序列化时特殊字符导致的异常问题，确保 SPC 系统中包含 `<`、`>`、`&` 等特殊字符的数据能够正常序列化和传输。

# Redis 配置参数说明

## 📋 完整配置模板

以下是在 Nacos 配置中心 `datasource-pgsql.yml` 中需要添加的完整 Redis 配置：

```yaml
# ========== Redis 基础配置 ==========
spring:
  redis:
    # Redis 服务器地址
    host: ************
    # Redis 服务器端口
    port: 6379
    # Redis 服务器密码（如果有密码则填写，无密码留空）
    password: 
    # 数据库索引（0-15）
    database: 0
    # 连接超时时间（毫秒）
    timeout: 10000
    
    # ========== Lettuce 连接池配置 ==========
    lettuce:
      pool:
        # 连接池最大连接数（-1表示无限制）
        max-active: 50
        # 连接池最大阻塞等待时间（毫秒，-1表示无限制）
        max-wait: 10000
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 5
      # 关闭超时时间（毫秒）
      shutdown-timeout: 5000

    # ========== Redisson 主客户端配置 ==========
    redisson:
      # 连接超时时间（毫秒）
      # 建议值：15000（15秒）- 解决网络延迟问题
      connect-timeout: 15000
      
      # 命令等待超时时间（毫秒）
      # 建议值：10000（10秒）- 解决 RedisResponseTimeoutException
      timeout: 10000
      
      # 命令失败重试次数
      # 建议值：5 - 增强容错能力
      retry-attempts: 5
      
      # 命令重试发送时间间隔（毫秒）
      # 建议值：2000（2秒）- 避免频繁重试
      retry-interval: 2000
      
      # 连接池大小
      # 建议值：50 - 支持高并发访问
      connection-pool-size: 50
      
      # 连接池中的最小空闲连接数
      # 建议值：10 - 保持足够的空闲连接
      connection-minimum-idle-size: 10
      
      # 空闲连接超时时间（毫秒）
      # 建议值：1800000（30分钟）
      idle-connection-timeout: 1800000
      
      # TCP_NODELAY 设置
      # 建议值：true - 减少网络延迟
      tcp-no-delay: true
      
      # TCP KeepAlive 设置
      # 建议值：true - 保持连接活跃
      keep-alive: true
      
      # Ping 连接检查间隔（毫秒）
      # 建议值：30000（30秒）- 定期检查连接状态
      ping-connection-interval: 30000
      
      # Ping 超时时间（毫秒）
      # 建议值：5000（5秒）
      ping-timeout: 5000
      
      # Netty 线程数（0表示自动计算：CPU核心数*2，最少8个）
      # 建议值：0（自动）或根据实际情况设置
      netty-threads: 0
      
      # 处理线程数（0表示自动计算：CPU核心数*2，最少8个）
      # 建议值：0（自动）或根据实际情况设置
      threads: 0
      
      # ========== 分布式锁专用配置 ==========
      lock:
        # 连接超时时间（毫秒）- 锁操作需要更快的响应
        connect-timeout: 5000
        
        # 命令等待超时时间（毫秒）- 锁操作超时时间
        timeout: 3000
        
        # 命令失败重试次数 - 锁操作重试次数
        retry-attempts: 3
        
        # 命令重试发送时间间隔（毫秒）
        retry-interval: 1000
        
        # 连接池大小 - 锁操作连接池
        connection-pool-size: 20
        
        # 连接池中的最小空闲连接数
        connection-minimum-idle-size: 5
        
        # 空闲连接超时时间（毫秒）- 10分钟
        idle-connection-timeout: 600000
        
        # Netty 线程数 - 锁操作专用线程
        netty-threads: 4
        
        # 处理线程数 - 锁操作处理线程
        threads: 4

    # ========== Redis 健康监控配置 ==========
    monitor:
      # 是否启用健康监控
      enabled: true

      # 健康检查间隔（毫秒）
      # 建议值：30000（30秒）
      check-interval: 30000

      # 健康检查使用的键名
      health-check-key: "redis:health:check"

      # 健康检查使用的值
      health-check-value: "OK"

      # 最大连续失败次数（超过此次数发出警告）
      max-consecutive-failures: 3

      # 测试数据TTL（秒）
      test-data-ttl: 10

    # ========== Redis 重试机制配置 ==========
    retry:
      # 是否启用重试机制
      enabled: true

      # 默认最大重试次数
      max-retries: 3

      # 初始重试延迟（毫秒）
      initial-delay: 1000

      # 重试延迟倍数（指数退避）
      delay-multiplier: 1.5
```

## 🔧 参数调优建议

### 🚀 高并发场景
```yaml
spring:
  redis:
    redisson:
      connection-pool-size: 100        # 增加连接池大小
      connection-minimum-idle-size: 20 # 增加最小空闲连接
      netty-threads: 16               # 增加网络线程数
      threads: 16                     # 增加处理线程数
```

### 🌐 网络不稳定场景
```yaml
spring:
  redis:
    redisson:
      connect-timeout: 20000          # 增加连接超时时间
      timeout: 15000                  # 增加命令超时时间
      retry-attempts: 8               # 增加重试次数
      retry-interval: 3000            # 增加重试间隔
```

### 💾 内存敏感场景
```yaml
spring:
  redis:
    redisson:
      connection-pool-size: 20        # 减少连接池大小
      connection-minimum-idle-size: 5 # 减少最小空闲连接
      idle-connection-timeout: 300000 # 减少空闲超时时间（5分钟）
```

### ⚡ 低延迟场景
```yaml
spring:
  redis:
    redisson:
      connect-timeout: 5000           # 减少连接超时时间
      timeout: 3000                   # 减少命令超时时间
      tcp-no-delay: true              # 启用 TCP_NODELAY
      ping-connection-interval: 10000 # 增加心跳频率
```

## 📊 监控指标说明

### 🔍 关键监控指标

1. **连接数监控**
   - `connection-pool-size`: 最大连接数
   - `connection-minimum-idle-size`: 最小空闲连接数
   - 实际使用连接数

2. **超时监控**
   - `connect-timeout`: 连接超时次数
   - `timeout`: 命令超时次数
   - 平均响应时间

3. **重试监控**
   - `retry-attempts`: 重试次数统计
   - 重试成功率
   - 重试原因分析

4. **线程监控**
   - `netty-threads`: Netty 线程使用率
   - `threads`: 处理线程使用率
   - 线程池队列长度

## 🚨 故障排查参数

### 连接超时问题
```yaml
# 临时调试配置 - 增加超时时间和重试
spring:
  redis:
    redisson:
      connect-timeout: 30000
      timeout: 20000
      retry-attempts: 10
      retry-interval: 5000
```

### 高并发问题
```yaml
# 临时调试配置 - 增加连接池和线程
spring:
  redis:
    redisson:
      connection-pool-size: 200
      connection-minimum-idle-size: 50
      netty-threads: 32
      threads: 32
```

### 网络不稳定问题
```yaml
# 临时调试配置 - 增强网络容错
spring:
  redis:
    redisson:
      tcp-no-delay: true
      keep-alive: true
      ping-connection-interval: 15000
      ping-timeout: 10000
```

## 📝 配置更新步骤

1. **在 Nacos 中更新配置**
   - 登录 Nacos 控制台：http://192.168.2.13:8848/nacos
   - 找到 `datasource-pgsql.yml` 配置文件
   - 添加或修改上述 Redis 配置

2. **验证配置生效**
   ```bash
   # 查看应用日志，确认配置加载
   tail -f logs/yingfei-*/info.log | grep -i redis
   ```

3. **监控效果**
   - 观察 Redis 连接异常是否减少
   - 监控应用响应时间是否改善
   - 查看健康检查日志输出

## ⚠️ 注意事项

1. **环境差异**：不同环境（开发/测试/生产）应使用不同的配置值
2. **资源限制**：连接池大小不要超过 Redis 服务器的最大连接数限制
3. **网络环境**：根据实际网络延迟调整超时时间
4. **业务特点**：根据业务访问模式调整连接池配置
5. **监控告警**：设置合适的监控阈值和告警规则

## 🔄 配置热更新

Nacos 配置支持热更新，修改配置后应用会自动重新加载 Redis 连接配置。如果需要立即生效，可以：

1. **重启应用**（推荐）
2. **调用健康检查接口**验证新配置
3. **观察监控指标**确认配置生效

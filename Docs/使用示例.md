# JSON 特殊字符转义使用示例

## 问题场景

在您的 SPC 系统中，`ExportImgDTO` 的 `contentList` 字段包含特殊字符时出现序列化异常：

```java
// 问题数据示例
List<List<Object>> contentList = Arrays.asList(
    Arrays.asList(">USL", null, 3, 0.75),
    Arrays.asList("<LSL", null, 1, 0.25)
);
```

## 解决方案

### 方案一：使用自定义序列化器（推荐）

**步骤 1**: 在 `ExportImgDTO` 中添加注解

```java
@Data
public class ExportImgDTO {
    // ... 其他字段
    
    /**
     * 列表内容 - 使用自定义序列化器处理特殊字符
     */
    @JsonSerialize(using = StringEscapeSerializer.class)
    private List<List<Object>> contentList = new ArrayList<>();
}
```

**步骤 2**: 正常使用，序列化器会自动处理特殊字符

```java
// 创建包含特殊字符的数据
ExportImgDTO dto = new ExportImgDTO();
dto.setContentList(Arrays.asList(
    Arrays.asList(">USL", null, 3, 0.75),
    Arrays.asList("<LSL", null, 1, 0.25),
    Arrays.asList("实际值>USL", "实际值<LSL", "实际值总数", "实际PPM")
));

// 序列化（特殊字符会自动转义）
ObjectMapper mapper = new ObjectMapper();
String json = mapper.writeValueAsString(dto);

// 结果：特殊字符被安全转义
// ">USL" -> "\u003eUSL"
// "<LSL" -> "\u003cLSL"
```

### 方案二：使用工具类手动处理

如果需要在其他地方处理特殊字符：

```java
// 单个字符串转义
String escaped = JsonEscapeUtil.escapeOptimized("<LSL");
System.out.println(escaped); // 输出: \u003cLSL

// 批量转义
String[] inputs = {">USL", "<LSL", "A & B"};
String[] escaped = JsonEscapeUtil.escapeArray(inputs);
// 结果: ["\u003eUSL", "\u003cLSL", "A \u0026 B"]

// 检查是否包含特殊字符
boolean hasSpecial = JsonEscapeUtil.containsSpecialCharacters("<LSL");
System.out.println(hasSpecial); // 输出: true
```

## 完整示例

```java
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yingfei.entity.dto.chart.ExportImgDTO;
import java.util.Arrays;

public class ExampleUsage {
    public static void main(String[] args) throws Exception {
        // 创建测试数据
        ExportImgDTO dto = new ExportImgDTO();
        dto.setImgType(6);
        dto.setChartName("帕累托图");
        dto.setTitleList(Arrays.asList("类别", "频数", "件", "百分比"));
        
        // 包含特殊字符的内容
        dto.setContentList(Arrays.asList(
            Arrays.asList(">USL", null, 3, 0.75),
            Arrays.asList("<LSL", null, 1, 0.25),
            Arrays.asList("实际值>USL", "实际值<LSL", "实际值总数", "实际PPM")
        ));
        
        // 序列化
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(dto);
        
        System.out.println("序列化成功！");
        System.out.println("JSON 长度: " + json.length());
        
        // 验证特殊字符已被转义
        System.out.println("包含转义的 >: " + json.contains("\\u003e"));
        System.out.println("包含转义的 <: " + json.contains("\\u003c"));
        System.out.println("不包含原始 >: " + !json.contains(">USL"));
        System.out.println("不包含原始 <: " + !json.contains("<LSL"));
    }
}
```

## 转义效果对比

### 转义前（会导致异常）
```json
{
  "contentList": [
    [">USL", null, 3, 0.75],
    ["<LSL", null, 1, 0.25]
  ]
}
```

### 转义后（安全序列化）
```json
{
  "contentList": [
    ["\u003eUSL", null, 3, 0.75],
    ["\u003cLSL", null, 1, 0.25]
  ]
}
```

## 性能说明

- **StringEscapeSerializer**: 专门优化，只在需要时进行转义
- **JsonEscapeUtil.escapeOptimized()**: 智能检测，无特殊字符时直接返回原字符串
- **内存友好**: 使用 StringBuilder 避免频繁字符串创建

## 注意事项

1. **显示问题**: 转义后的字符串在前端显示时可能需要反转义
2. **兼容性**: 转义后的 JSON 完全兼容标准 JSON 解析器
3. **扩展性**: 可以根据需要添加更多特殊字符的处理规则

## 故障排除

如果仍然遇到问题：

1. **确认注解位置**: `@JsonSerialize` 注解必须在字段上
2. **检查导入**: 确保导入了正确的类
3. **验证数据**: 使用 `JsonEscapeUtil.containsSpecialCharacters()` 检查数据
4. **日志调试**: 在序列化前后打印数据进行对比

通过这套解决方案，您的 SPC 系统应该能够正常处理包含特殊字符的数据了。

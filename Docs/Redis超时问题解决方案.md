# Redis 超时问题解决方案

## 🔍 问题分析

您遇到的 `RedisResponseTimeoutException` 错误表明：

```
Caused by: org.redisson.client.RedisResponseTimeoutException: 
Redis server response timeout (3000 ms) occured after 3 retry attempts, 
is non-idempotent command: false 
Check connection with Redis node: ************/************:6379 for TCP packet drops.  
Try to increase nettyThreads and/or timeout settings.
```

**问题根因：**
1. **Redis 服务器响应超时**：3000ms (3秒) 超时时间过短
2. **重试机制失效**：已经重试了 3 次仍然失败
3. **网络连接问题**：可能存在 TCP 包丢失
4. **Redisson 配置不当**：缺少合适的超时和线程配置

## 🛠 解决方案

### 1. 新增 Redisson 优化配置

已创建 `RedissonConfig.java` 配置类，包含以下优化：

#### 🔧 核心超时配置
- **连接超时时间**：从 10000ms 增加到 15000ms
- **命令等待超时**：从 3000ms 增加到 10000ms
- **重试次数**：从 3 次增加到 5 次
- **重试间隔**：从 1500ms 增加到 2000ms

#### 🚀 连接池优化
- **连接池大小**：增加到 50 个连接
- **最小空闲连接**：保持 10 个空闲连接
- **空闲连接超时**：30 分钟
- **连接最大存活时间**：1 小时

#### 🧵 线程配置优化
- **Netty 线程数**：根据 CPU 核心数自动调整（最少 8 个）
- **处理线程数**：根据 CPU 核心数自动调整（最少 8 个）

### 2. Redis 健康监控

已创建 `RedisHealthMonitor.java` 监控组件：

- **定期健康检查**：每 30 秒检查一次 Redis 连接状态
- **故障检测**：连续失败 3 次后发出警告
- **连接统计**：提供详细的连接状态信息
- **自动恢复检测**：连接恢复时自动重置失败计数

### 3. 智能重试机制

已创建 `RedisRetryUtil.java` 重试工具：

- **智能重试判断**：根据异常类型决定是否重试
- **指数退避策略**：重试间隔递增，避免雪崩
- **详细错误日志**：提供友好的错误描述
- **灵活配置**：支持自定义重试次数和延迟时间

### 4. 增强版 Redis 服务

已创建 `EnhancedRedisService.java`：

- **所有操作都带重试机制**
- **统一的错误处理**
- **详细的操作日志**
- **分布式锁优化**

## 📋 配置步骤

### 1. 在 Nacos 中添加 Redis 配置

在 Nacos 配置中心添加以下配置（`datasource-pgsql.yml`）：

```yaml
# Redis 配置
spring:
  redis:
    # Redis 服务器地址
    host: ************
    # Redis 服务器端口
    port: 6379
    # Redis 服务器密码（如果有）
    password: 
    # 数据库索引
    database: 0
    # 连接超时时间（毫秒）
    timeout: 10000
    
    # Lettuce 连接池配置
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 50
        # 连接池最大阻塞等待时间（毫秒）
        max-wait: 10000
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 5
      # 关闭超时时间（毫秒）
      shutdown-timeout: 5000

# Redisson 配置（可选，如果需要更细粒度控制）
redisson:
  # 单机模式配置
  single-server-config:
    # 连接超时时间（毫秒）
    connect-timeout: 15000
    # 命令等待超时时间（毫秒）
    timeout: 10000
    # 重试次数
    retry-attempts: 5
    # 重试间隔（毫秒）
    retry-interval: 2000
    # 连接池大小
    connection-pool-size: 50
    # 最小空闲连接数
    connection-minimum-idle-size: 10
```

### 2. 更新自动配置

在 `yingfei-common-redis/src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 中添加：

```
com.yingfei.common.redis.configure.RedissonConfig
com.yingfei.common.redis.monitor.RedisHealthMonitor
com.yingfei.common.redis.service.EnhancedRedisService
```

### 3. 使用增强版服务

在业务代码中使用 `EnhancedRedisService` 替代原有的 `RedisService`：

```java
@Autowired
private EnhancedRedisService enhancedRedisService;

// 设置缓存（自动重试）
enhancedRedisService.setCacheObject("key", value, 300, TimeUnit.SECONDS);

// 获取缓存（自动重试）
String result = enhancedRedisService.getCacheObject("key");

// 使用自定义重试配置
String result = enhancedRedisService.retryBuilder()
    .maxRetries(5)
    .initialDelay(2000)
    .execute(() -> redisTemplate.opsForValue().get("key"), "获取关键数据");
```

## 🔧 网络层面优化建议

### 1. 检查网络连接
```bash
# 测试到 Redis 服务器的连接
ping ************

# 测试 Redis 端口连通性
telnet ************ 6379

# 检查网络延迟
traceroute ************
```

### 2. Redis 服务器优化

在 Redis 服务器上进行以下配置优化：

```bash
# 编辑 Redis 配置文件
sudo vim /etc/redis/redis.conf

# 添加或修改以下配置
timeout 300                    # 客户端空闲超时时间（秒）
tcp-keepalive 60              # TCP keepalive 时间
tcp-backlog 511               # TCP 监听队列长度
maxclients 10000              # 最大客户端连接数

# 重启 Redis 服务
sudo systemctl restart redis
```

### 3. 系统层面优化

```bash
# 增加系统文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

## 📊 监控和诊断

### 1. 查看 Redis 连接状态

```java
@Autowired
private RedisHealthMonitor redisHealthMonitor;

// 获取连接统计信息
String stats = redisHealthMonitor.getConnectionStats();
log.info("Redis 连接状态: {}", stats);

// 手动触发健康检查
boolean isHealthy = redisHealthMonitor.manualHealthCheck();
```

### 2. 监控关键指标

- **连接数**：监控 Redis 服务器的连接数
- **响应时间**：监控 Redis 操作的响应时间
- **错误率**：监控 Redis 操作的失败率
- **网络延迟**：监控网络延迟情况

## 🚨 故障排查步骤

1. **检查 Redis 服务状态**
   ```bash
   redis-cli -h ************ -p 6379 ping
   ```

2. **查看 Redis 服务器日志**
   ```bash
   tail -f /var/log/redis/redis-server.log
   ```

3. **检查网络连接**
   ```bash
   netstat -an | grep 6379
   ```

4. **查看应用日志**
   - 关注 Redis 连接相关的错误日志
   - 查看健康检查的输出

5. **检查系统资源**
   ```bash
   # 检查内存使用
   free -h
   
   # 检查 CPU 使用
   top
   
   # 检查网络连接数
   ss -s
   ```

## ✅ 预期效果

实施以上解决方案后，您应该看到：

1. **Redis 超时异常大幅减少**
2. **连接稳定性显著提升**
3. **系统响应时间更加稳定**
4. **更好的错误恢复能力**
5. **详细的监控和诊断信息**

如果问题仍然存在，请检查：
- Redis 服务器的负载情况
- 网络设备的配置
- 防火墙设置
- Redis 服务器的内存使用情况
